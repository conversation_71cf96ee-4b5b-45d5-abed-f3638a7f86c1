CREATE MATERIALIZED VIEW shopify.mv_pixel_events ON CLUSTER 'default_cluster'
TO shopify.pixel_events
AS
SELECT
  sg_event_id,
  coalesce(sg_session_id, '') AS sg_session_id,
  coalesce(id, '') AS id,
  coalesce(client_id, '') AS client_id,
  coalesce(sg_new_client_id, '') AS sg_new_client_id,
  coalesce(context, '{}') AS context,
  coalesce(data, '{}') AS data,
  coalesce(type, '') AS type,
  coalesce(name, '') AS name,
  parseDateTime64BestEffort(coalesce(timestamp, '1970-01-01 00:00:00'), 3) AS timestamp,
  coalesce(seq, 0) AS seq,
  coalesce(shop_domain, '') AS shop_domain,
  coalesce(cart, '{}') AS cart,
  coalesce(extra, '{}') AS extra
FROM shopify.pixel_events_stream 
WHERE sg_event_id IS NOT NULL AND sg_event_id != ''
AND sg_session_id IS NOT NULL AND sg_session_id != '';