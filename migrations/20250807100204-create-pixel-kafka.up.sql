CREATE TABLE shopify.pixel_events_stream ON CLUSTER 'default_cluster'
(
  `sg_event_id` String,
  `sg_session_id` String,
  `id` String,
  `client_id` String,
  `sg_new_client_id` String,
  `context` String,
  `data` String,
  `type` String,
  `name` String,
  `timestamp` String,
  `seq` Int32,
  `shop_domain` String,
  `cart` String,
  `extra` String
)
ENGINE = Kafka
SETTINGS
  kafka_broker_list = '10.13.32.22:9092',
  kafka_topic_list = 'shopify-web-pixel-event',
  kafka_group_name = 'ck-shopify-pixel',
  kafka_format = 'JSONEachRow',
  kafka_num_consumers = 1;