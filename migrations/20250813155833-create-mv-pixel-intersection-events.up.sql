CREATE MATERIALIZED VIEW shopify.mv_pixel_intersection_events ON CLUSTER 'default_cluster'
TO shopify.pixel_intersection_events
AS
SELECT
  date,
  sg_event_id,
  sg_session_id,
  sg_new_client_id,
  JSONExtractString(data, 'sgElementId') AS sg_element_id,
  JSONExtractString(data, 'sgExtraData', 'element_tag') AS element_tag,
  JSONExtractString(data, 'sgExtraData', 'product_id') AS product_id,
  JSONExtractString(data, 'sgExtraData', 'main_image_index') AS main_image_index,
  coalesce(JSONExtractFloat(data, 'intersectionRatio'), 0.0) AS intersection_ratio,
  context,
  data,
  type,
  name,
  timestamp,
  shop_domain,
  extra
FROM shopify.pixel_events
WHERE sg_event_id != '' AND sg_event_id IS NOT NULL
AND sg_session_id != '' AND sg_session_id IS NOT NULL
AND name = 'sg:dom-intersection'
AND <PERSON>SON<PERSON>as(data, 'sgElementId')
AND JSONExtractString(data, 'sgElementId') != ''
AND JSONExtractString(data, 'isIntersecting') = 'true'
AND JSONExtractString(context, 'navigator', 'userAgent') NOT LIKE '%Windows NT%'
AND JSONExtractString(context, 'navigator', 'userAgent') NOT LIKE '%Macintosh%'
AND JSONExtractString(context, 'navigator', 'userAgent') NOT LIKE '%X11%'
AND JSONExtractString(context, 'navigator', 'userAgent') NOT LIKE '%Linux x86_64%'
AND JSONExtractString(context, 'navigator', 'userAgent') NOT LIKE '%Electron%';
