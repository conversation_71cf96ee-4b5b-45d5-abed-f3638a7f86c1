-- 元素曝光事件
CREATE TABLE shopify.pixel_intersection_events ON CLUSTER 'default_cluster'
(
  `date` Date,
  `sg_event_id` String,
  `sg_session_id` String,
  `sg_new_client_id` String,
  `sg_element_id` String,
  `element_tag` String,
  `product_id` String,
  `intersection_ratio` Float32,
  `context` String,
  `data` String,
  `type` String,
  `name` String,
  `timestamp` DateTime64(3),
  `shop_domain` String,
  `extra` String,
)
ENGINE = ReplicatedReplacingMergeTree()
ORDER BY (date, shop_domain, element_tag, sg_session_id, sg_element_id, sg_event_id);
