CREATE TABLE shopify.product_viewed_events ON CLUSTER 'default_cluster'
(
  `sg_event_id` String,
  `id` String,
  `shop_domain` String,
  `client_id` String,
  `sg_new_client_id` String,
  `product_id` String,
  `sku_id` String,
  `timestamp` DateTime64(3),
  `date` Date MATERIALIZED toDate(timestamp)
)
ENGINE = ReplicatedReplacingMergeTree()
PARTITION BY toYYYYMMDD(date)
ORDER BY (shop_domain, sg_new_client_id, sg_event_id);
