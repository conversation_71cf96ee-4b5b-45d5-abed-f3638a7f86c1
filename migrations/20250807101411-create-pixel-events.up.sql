-- 页面事件（包含页面展示/点击/元素曝光）
CREATE TABLE shopify.pixel_events ON CLUSTER 'default_cluster'
(
  `date` Date MATERIALIZED toDate(timestamp),
  `sg_event_id` String,
  `sg_session_id` String,
  `id` String,
  `client_id` String,
  `sg_new_client_id` String,
  `context` String,
  `data` String,
  `type` String,
  `name` String,
  `timestamp` DateTime64(3),
  `seq` Int32,
  `shop_domain` String,
  `cart` String,
  `extra` String
)
ENGINE = ReplicatedReplacingMergeTree()
ORDER BY (date, shop_domain, name, sg_session_id, sg_event_id);

--- shopify.pixel_events.data sg:dom-intersection 结构
```
{
    "sgElementId": "c47edf6093f3478c82bc2d0b51cc1a27",
    "sgExtraData": {
        "element_tag": "\u4e3b\u56fe",
        "product_id": 7494412075179,
        "main_image_url": "https://www.ororowear.ca/cdn/shop/products/6_e8390183-73ac-4bdb-a47c-a165231311de.jpg?v=1753424336&width=1946",
        "selling_point_summary": null,
        "bounding_box": null
    },
    "duration": 0,
    "isIntersecting": true,
    "intersectionRatio": 0.4961523115634918
}
```