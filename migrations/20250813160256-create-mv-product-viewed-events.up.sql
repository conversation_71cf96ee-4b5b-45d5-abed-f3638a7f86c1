CREATE MATERIALIZED VIEW shopify.mv_product_viewed_events ON CLUSTER 'default_cluster'
TO shopify.product_viewed_events
AS
SELECT
  sg_event_id,
  id,
  shop_domain,
  client_id,
  sg_new_client_id,
  coalesce(JSONExtractString(data, 'productVariant', 'product', 'id'), '') AS product_id,
  coalesce(JSONExtractString(data, 'productVariant', 'id'), '') AS sku_id,
  timestamp
FROM shopify.pixel_events
WHERE 
    name = 'product_viewed' 
    AND type = 'standard'
    AND sg_event_id IS NOT NULL AND sg_event_id != ''
    AND id IS NOT NULL AND id != ''
    AND sg_new_client_id IS NOT NULL AND sg_new_client_id != ''
    AND JSONHas(data, 'productVariant');
