from datetime import tzinfo, timezone
from enum import StrEnum
from typing import Any, ClassVar, Annotated

from pydantic import ConfigD<PERSON>, TypeAdapter, BeforeValidator
from sqlalchemy import TypeDecorator
from sqlmodel import SQLModel, Field, Relationship, JSON, Column

from src.constants import utc_tz
from src.utils import safe_number_str


class PydanticJson(TypeDecorator):
    impl = JSON()
    cache_ok = True

    def __init__(self, pt):
        super().__init__()
        self.pt = TypeAdapter(pt)
        self.coerce_compared_value = self.impl.coerce_compared_value

    def bind_processor(self, dialect):
        return lambda value: self.pt.dump_json(value) if value is not None else None

    def result_processor(self, dialect, coltype):
        return lambda value: self.pt.validate_json(value) if value is not None else None


class JWTPayloadStoreInfo(SQLModel):
    storeId: int
    domain: str


class JWTPayload(SQLModel):
    storeInfo: JWTPayloadStoreInfo
    orgId: int
    userId: int


class Dashboard(SQLModel, table=True):
    """用户自定义的数据看板"""
    __tablename__ = "customize_bi_dashboard"
    id: int = Field(primary_key=True)
    slices: list["Slice"] = Relationship(back_populates="dashboard")


class Slice(SQLModel, table=True):
    """用户自定义的数据表"""
    __tablename__ = "customize_bi_slice"
    id: int = Field(primary_key=True)
    dashboard_id: int = Field(foreign_key="customize_bi_dashboard.id")
    dashboard: Dashboard = Relationship(back_populates="slices")


class SQLTableColumn(SQLModel, table=True):
    """维护数据表字段信息，代表一个“列”，它既可以对应数据库中一个真实的物理列，也可以是一个通过 SQL 表达式定义的“计算列”"""
    __tablename__ = "sql_table_columns"
    id: int = Field(primary_key=True)
    table_id: int = Field(foreign_key="sql_tables.id", ondelete="CASCADE")
    column_name: str = Field(description="数据库中原始的列名")
    label: str = Field(description="UI 展示名称")
    description: str = Field(description="对该列的详细描述，通常在 UI 中作为提示（tooltip）信息显示")
    type: str = Field(description="数据类型标记（来自数据库列类型）")
    expression: str | None = Field(None,
                                   description="f(x) SQL Expression, e.g.: `case when gender=1 then 'Male' else 'Female' end`")
    is_datetime: bool = Field(description="一个布尔标记，用于指明该列是否为日期/时间类型，这对于时间序列图表至关重要")
    python_date_format: str | None = Field(None, description="如果是日期/时间列，这里可以定义其解析格式")


class SQLMetric(SQLModel, table=True):
    """代表一个“指标”，它也是一种 f(x)，但特指聚合函数。它和计算列的核心区别是，指标用于图表的“Metrics”区域，而计算列和物理列一样，可以用作维度（Dimension）或指标"""
    __tablename__ = "sql_metrics"

    id: int = Field(primary_key=True)
    table_id: int = Field(foreign_key="sql_tables.id", ondelete="CASCADE")

    metric_name: str = Field(description="指标的名称，如 SUM(sales) 的 metric_name 可以是 '总销售额'")
    expression: str = Field(description="定义聚合的 SQL 表达式，例如 SUM(sales) 或 COUNT(DISTINCT user_id)")
    description: str = Field(description="对该指标的描述")


class SQLTable(SQLModel, table=True):
    __tablename__ = "sql_tables"

    id: int = Field(primary_key=True)


class QueryBuilderDatasource(SQLModel):
    pass


class QueryFilter(SQLModel):
    col: str
    op: str
    val: Any


class TimeRangeValue(SQLModel):
    model_config = ConfigDict(ignored_types=(tzinfo,), arbitrary_types_allowed=True)

    low: str
    high: str

    tz: timezone = Field(utc_tz, exclude=True)

    def model_post_init(self, context):
        from src.utils import TimeRangeHelper

        self.tz = TimeRangeHelper.extract_tz_info(self.low)
        # 将字符串的本地日期时间转换为 UTC 日期时间字符串
        self.low = TimeRangeHelper.to_str(TimeRangeHelper.to_utc(TimeRangeHelper.from_str(self.low, self.tz)))
        self.high = TimeRangeHelper.to_str(TimeRangeHelper.to_utc(TimeRangeHelper.from_str(self.high, self.tz)))


class Option(SQLModel):
    label: str
    value: str | None


class TemplateQueryUserTypeEnum(StrEnum):
    """区分客户类型为新客/老客"""
    NEW = "new"
    RETURNING = "returning"
    ALL = "all"

    @classmethod
    def get_options(cls):
        return [
            Option(label="new", value=cls.NEW),
            Option(label="returning", value=cls.RETURNING),
            Option(label="All Users", value=cls.ALL)
        ]


class TemplateQueryTrafficSourceEnum(StrEnum):
    """访问来源渠道: 站内搜索 / 首页推荐 / Push / 广告 / 外部跳转 / 其他"""
    INSITE_SEARCH = "insite_search"
    HOMEPAGE_RECOMMENDATION = "homepage_recommendation"
    PUSH = "push"
    AD = "ad"
    EXTERNAL_LINK = "external_link"
    OTHER = "other"
    ALL = "all"

    @classmethod
    def get_options(cls):
        return [
            Option(label="Insite Search", value=cls.INSITE_SEARCH),
            Option(label="Homepage Recommendation", value=cls.HOMEPAGE_RECOMMENDATION),
            Option(label="Push", value=cls.PUSH),
            Option(label="Ad", value=cls.AD),
            Option(label="External Link", value=cls.EXTERNAL_LINK),
            Option(label="Other", value=cls.OTHER),
            Option(label="All Sources", value=cls.ALL)
        ]


class ElementTagEnum(StrEnum):
    MAIN_IMAGE = "主图"  # 主图
    PRICE_AND_DISCOUNT = "商品价格与折扣"  # 商品价格与折扣
    SKU_AREA = "SKU区域"  # SKU区域
    ADD_TO_CART_AREA = "加购区域"  # 加购区域
    PRODUCT_FEATURES = "商品功能列表"  # 商品功能列表
    PACKING_LIST = "货品清单"  # 货品清单
    WARRANTY_RETURN_POLICY = "保修和退换政策"  # 保修和退换政策
    SELLER_POINTS = "商品卖点"  # 商品卖点
    SIZE_CHART = "尺码表"  # 尺码表
    FAQ_SECTION = "FAQ列表"  # FAQ列表
    RELATED_RECOMMENDATIONS = "相关推荐列表"  # 相关推荐列表
    COMMENT_SECTION = "评论区"  # 评论区

    @classmethod
    def get_options(cls):
        return [
            Option(label=" ".join(map(lambda s: s.title(), option.name.split("_"))), value=option.value)
            for option in cls
        ]


class TemplateQueryFilter(SQLModel):
    time_range: TimeRangeValue
    user_type: TemplateQueryUserTypeEnum | None = None
    traffic_source: TemplateQueryTrafficSourceEnum | None = None
    user_location: str | list[str] | None = None


class IQueryContext(SQLModel):
    pass


class QueryContext(IQueryContext):
    # dimensions & group by
    group_by: list[SQLTableColumn]
    # 指标
    metrics: list[SQLMetric]
    # 自定筛选
    adhoc_filters: list[QueryFilter]
    # 全局筛选
    global_filters: list[QueryFilter]
    # 排序 [metric, is ascending]
    order_by: list[tuple[SQLMetric, bool]]
    row_limit: int = 1000
    row_offset: int = 0


class TemplateQueryContext(IQueryContext):
    global_filters: TemplateQueryFilter
    product_id: int | str | None = Field(default=None)
    element_tag: str | None = Field(default=None)


class QueryResult(SQLModel):
    data: list[dict]


class TemplateQueryParams(SQLModel):
    timestamp: dict
    shop_domain: str
    element_tag: str | None
    product_id: str | None
    traffic_source: str


class TemplateKeyMetricsOverViewData(SQLModel):
    QUERY_ID: ClassVar[str] = "1"

    visitors: Annotated[str, BeforeValidator(safe_number_str)]
    page_views: Annotated[str, BeforeValidator(safe_number_str)]
    avg_session_duration: Annotated[str, BeforeValidator(safe_number_str)]
    bounce_rate: Annotated[str, BeforeValidator(safe_number_str)]
    non_active_bounce_rate: Annotated[str, BeforeValidator(safe_number_str)]
    add_to_cart_rate: Annotated[str, BeforeValidator(safe_number_str)]
    conversion_rate: Annotated[str, BeforeValidator(safe_number_str)]

    def to_last_period(self):
        return {f"{key}_last_period": val for key, val in self.model_dump().items()}


class TemplateKeyMetricsOverViewResponse(SQLModel):
    """官方报表 - Section 1 Key Metrics Overview"""
    visitors: str
    visitors_last_period: str
    page_views: str
    page_views_last_period: str
    avg_session_duration: str
    avg_session_duration_last_period: str
    bounce_rate: str
    bounce_rate_last_period: str
    non_active_bounce_rate: str
    non_active_bounce_rate_last_period: str
    add_to_cart_rate: str
    add_to_cart_rate_last_period: str
    conversion_rate: str
    conversion_rate_last_period: str


class TemplateDurationDistributeData(SQLModel):
    QUERY_ID: ClassVar[str] = "2"

    duration_distribute: str
    exposed_users: Annotated[str, BeforeValidator(safe_number_str)]
    add_to_cart_rate: Annotated[str, BeforeValidator(safe_number_str)]
    conversion_rate: Annotated[str, BeforeValidator(safe_number_str)]


class TemplateDurationDistributeResponse(SQLModel):
    """官方报表 - 停留时长分析"""
    duration_range: str
    exposed_users: str
    add_to_cart: str
    conversion: str


class TemplateProductAggregationData(SQLModel):
    QUERY_ID: ClassVar[str] = "6"

    product_id: Annotated[str, BeforeValidator(safe_number_str)]
    pathname: str
    exposures: Annotated[str, BeforeValidator(safe_number_str)]
    bounce_rate: Annotated[str, BeforeValidator(safe_number_str)]
    non_active_bounce_rate: Annotated[str, BeforeValidator(safe_number_str)]
    add_to_cart_rate: Annotated[str, BeforeValidator(safe_number_str)]
    conversion_rate: Annotated[str, BeforeValidator(safe_number_str)]
    avg_duration: Annotated[str, BeforeValidator(safe_number_str)]


class TemplateProductAggregationResponse(SQLModel):
    """官方报表 - Section 2 All Products Aggregation Analysis"""
    product_id: str
    product_name: str
    exposures: str
    avg_duration: str
    bounce_rate: str
    non_active_bounce_rate: str
    add_to_cart: str
    conversion: str
    product_path: str = ""


class TemplateProductPerformanceData(SQLModel):
    QUERY_ID: ClassVar[str] = "7"
    element_id: str
    exposed_users: Annotated[str, BeforeValidator(safe_number_str)]
    intersection_duration: Annotated[str, BeforeValidator(safe_number_str)]
    bounce_rate: Annotated[str, BeforeValidator(safe_number_str)]
    add_to_cart_rate: Annotated[str, BeforeValidator(safe_number_str)]
    conversion_rate: Annotated[str, BeforeValidator(safe_number_str)]


class ElementInfo(SQLModel):
    selectors: list[str] = Field(default_factory=list)
    main_image_url: str | None = None


class InsightInfo(SQLModel):
    # HIGH_VALUE_SELLING_POINT_SHORT_OF_EXPOSURE -- 高价值卖点低曝光
    insight_type: str
    insight_data: dict


class TemplateProductPerformanceResponse(SQLModel):
    """官方报表 - Section 3 Product Performance"""
    element_id: str
    element_name: str
    exposures: str
    avg_duration: str
    bounce_rate: str
    add_to_cart: str
    conversion: str
    element_info: ElementInfo = Field(default_factory=ElementInfo)
    insights: list[InsightInfo]


class TemplateMainImageData(SQLModel):
    QUERY_ID: ClassVar[str] = "4"
    main_image_index: Annotated[str, BeforeValidator(safe_number_str)]
    exposed_users: Annotated[str, BeforeValidator(safe_number_str)]
    intersection_duration: Annotated[str, BeforeValidator(safe_number_str)]
    bounce_rate: Annotated[str, BeforeValidator(safe_number_str)]
    add_to_cart_rate: Annotated[str, BeforeValidator(safe_number_str)]
    conversion_rate: Annotated[str, BeforeValidator(safe_number_str)]


class TemplateMainImageResponse(SQLModel):
    """官方报表 - 主图分析"""
    main_image_index: str
    image_number: str
    exposed_users: str
    avg_duration: str
    bounce_rate: str
    add_to_cart: str
    conversion: str


class ShopifySite(SQLModel, table=True):
    __tablename__ = "shopify_site"
    id: int = Field(primary_key=True)
    domain: str
    shopify_domain: str
    pages: list["ShopifyPage"] = Relationship(back_populates="site")


class ShopifyPageData(SQLModel):
    product_id: int | None = None


class ShopifyPage(SQLModel, table=True):
    __tablename__ = "shopify_page"
    id: int = Field(primary_key=True)
    site_id: int = Field(foreign_key="shopify_site.id")
    site: ShopifySite = Relationship(back_populates="pages")
    page_url: str
    data: ShopifyPageData = Field(sa_column=Column(PydanticJson(ShopifyPageData)))


class PageElementData(SQLModel):
    element_tag: str
    product_id: int | None
    main_image_url: str | None
    main_image_index: int | None
    selling_point_summary: str | None
    bounding_box: dict | None


class PageElement(SQLModel, table=True):
    __tablename__ = "page_element"
    id: int = Field(primary_key=True)
    target: str
    selectors: list[str] = Field(sa_column=Column(JSON()))
    data: PageElementData = Field(sa_column=Column(PydanticJson(PageElementData)))


class ProductPageElementStatus(StrEnum):
    READY = "ready"
    NOT_READY = "not_ready"


class ProductMetadata(SQLModel, table=True):
    __tablename__ = "product_metadata"
    id: int = Field(primary_key=True)
    store_domain: str
    product_url: str
    product_id: str
    product_title: str
    page_element_status: ProductPageElementStatus


class ProductTitleUrl(ProductMetadata):
    # omit these field
    id: ClassVar[int] = 0
    store_domain: ClassVar[str] = ""


class SPU(SQLModel, table=True):
    __tablename__ = "spu"
    id: int = Field(primary_key=True)
    spu_id: str
    spu_name: str
    domain: str
    handle: str
