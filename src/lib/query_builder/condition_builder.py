from functools import reduce
from typing import Any

from sqlglot import exp

from src import models
from src.utils import TimeRangeHelper


def filter_value(value, column: str | exp.Column, *, date_column: str | exp.Column | None = None):
    match value:
        case {"low": str(), "high": str()}:
            return filter_time_range(value=models.TimeRangeValue.model_validate(value), column=column,
                                     date_column=date_column)
        case models.TimeRangeValue():
            return filter_time_range(value=value, column=column, date_column=date_column)
        case list():
            return filter_values(value, column)
        case None:
            return exp.Is(this=_to_this(column), expression=exp.Null())
        case str() | int() | float():
            return exp.EQ(this=_to_this(column), expression=_to_literal(value))
        case _:
            raise ValueError(f"invalid filter value. {value}")


def filter_values(values: list, column: str | exp.Column):
    if not values:
        return exp.EQ(this=exp.Literal.number(1), expression=exp.Literal.number(0))
    elif len(values) == 1:
        return filter_value(column, values[0])
    else:
        return exp.In(this=_to_this(column), expressions=[_to_literal(v) for v in values])


def filter_time_range(value: models.TimeRangeValue, column: str | exp.Column, date_column: str | exp.Column | None):
    """因为 timestamp 的 column 没有索引，需要添加对应的 date 列查询"""
    cond = exp.Between(
        this=_to_this(column),
        low=exp.Literal.string(value.low),
        high=exp.Literal.string(value.high),
    )
    if date_column:
        date_list = TimeRangeHelper.get_date_list(value)
        if len(date_list) == 1:
            date_cond = filter_value(date_list[0], date_column)
        else:
            date_cond = exp.Between(
                this=_to_this(date_column),
                low=_to_literal(date_list[0]),
                high=_to_literal(date_list[-1])
            )
        cond = exp.And(this=cond, expression=date_cond)
    return cond


def concat_and(conditions: list):
    return reduce(lambda left, right: exp.And(this=left, expression=right), conditions)


def combine_conditions(conditions: list):
    return exp.Where(this=reduce(
        lambda left, right: exp.And(this=left, expression=right),
        conditions
    ))


def _to_literal(value: Any) -> exp.Literal:
    if isinstance(value, str):
        return exp.Literal.string(value)
    return exp.Literal.number(value)


def _to_this(column: str | exp.Column):
    if isinstance(column, str):
        return exp.to_identifier(column)
    else:
        return column
