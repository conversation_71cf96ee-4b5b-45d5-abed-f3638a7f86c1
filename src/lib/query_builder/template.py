import textwrap
from pathlib import Path
from typing import Any

from asynch import Pool as ChPool
from jinja2 import Environment, FileSystemLoader, TemplateNotFound, Template
from sqlglot import exp, parse_one as _parse_one

from src import models, lib

DIALECT = "clickhouse"
raw = lambda _sql: _parse_one(_sql, read=DIALECT, dialect=DIALECT)


def query_context_to_template_params(shop_domain: str,
                                   query_context: models.TemplateQueryContext):
    params = models.TemplateQueryParams(
        timestamp={
            "start": query_context.global_filters.time_range.low,
            "end": query_context.global_filters.time_range.high
        },
        shop_domain=shop_domain,
        element_tag=query_context.element_tag,
        product_id=query_context.product_id,
        traffic_source='all'
    )
    traffic_source = transfer_traffic_source_to_sql(
        query_context.global_filters.traffic_source)
    if traffic_source != "all":
        params.traffic_source = traffic_source
    return params


def transfer_traffic_source_to_sql(
        traffic_source: models.TemplateQueryTrafficSourceEnum) -> str:
    if traffic_source == models.TemplateQueryTrafficSourceEnum.INSITE_SEARCH:
        return "ON_SITE_SEARCH"
    elif traffic_source == models.TemplateQueryTrafficSourceEnum.EXTERNAL_LINK:
        return "EXTERNAL_REFERRAL"
    elif traffic_source == models.TemplateQueryTrafficSourceEnum.HOMEPAGE_RECOMMENDATION:
        return "HOMEPAGE_RECOMMENDATION"
    elif traffic_source == models.TemplateQueryTrafficSourceEnum.PUSH:
        return "PUSH_NOTIFICATION"
    elif traffic_source == models.TemplateQueryTrafficSourceEnum.AD:
        return "ADVERTISEMENT"
    elif traffic_source == models.TemplateQueryTrafficSourceEnum.OTHER:
        return "OTHER"
    return "all"


async def query_key_metric_overview(conn_pool: ChPool, params: models.TemplateQueryParams):
    template = Template(textwrap.dedent("""\
    WITH base AS (
      SELECT
        *
      FROM
        pixel_events join shopify.session_channel_view  on pixel_events.sg_session_id = shopify.session_channel_view.sg_session_id
      WHERE
        shop_domain = '{{ shop_domain }}'
        AND date between date('{{ timestamp.start }}')
        and date('{{ timestamp.end }}')
        AND timestamp between '{{ timestamp.start }}'
        and '{{ timestamp.end }}'
        AND JSONExtractString(context, 'navigator', 'userAgent') NOT LIKE '%Windows NT%'
        AND JSONExtractString(context, 'navigator', 'userAgent') NOT LIKE '%Macintosh%'
        AND JSONExtractString(context, 'navigator', 'userAgent') NOT LIKE '%X11%'
        AND JSONExtractString(context, 'navigator', 'userAgent') NOT LIKE '%Linux x86_64%'
        AND JSONExtractString(context, 'navigator', 'userAgent') NOT LIKE '%Electron%'
        AND ('{{traffic_source}}' = 'all' OR shopify.session_channel_view.channel = '{{ traffic_source }}')
    ),
    page_viewd_standard AS (
      SELECT
        COUNT(DISTINCT sg_new_client_id) as visitors,
        COUNT(*) as page_views
      FROM
        base
      WHERE
        name = 'page_viewed'
        AND type = 'standard'
    ),
    session_grouped AS (
      SELECT
        AVG(duration) as avg_duration
      FROM
        (
          SELECT
            dateDiff('millisecond', MIN(timestamp), MAX(timestamp)) AS duration
          FROM
            base
          GROUP BY
            shop_domain,
            sg_session_id
        )
    ),
    page_viewed AS (
      SELECT
        SUM(
          CASE
            WHEN path_count = 1 THEN 1
            ELSE 0
          END
        ) / COUNT(*) as bounce_rate
      FROM
        (
          SELECT
            sg_session_id,
            COUNT(
              DISTINCT JSONExtractString(context, 'document', 'location', 'pathname')
            ) AS path_count
          FROM
            base
          WHERE
            name = 'page_viewed'
          GROUP BY
            sg_session_id
        )
    ),
    non_active_page_viewed AS (
      SELECT
        SUM(
          CASE
            WHEN has_sg_dom <= 1 THEN 1
            ELSE 0
          END
        ) / COUNT(DISTINCT sg_session_id) AS bounce_rate
      FROM
        (
          SELECT
            sg_session_id,
            SUM(
              CASE
                WHEN name IN (
                  'sg:heartbeat',
                  'sg:dom-intersection',
                  'sg:dom-touchstart',
                  'sg:dom-touchmove',
                  'sg:dom-touchend',
                  'sg:dom-touchcancel',
                  'product_viewed',
                  'collection_viewed',
                  'cart_viewed'
                ) THEN 0
                ELSE 1
              END
            ) AS has_sg_dom
          FROM
            base
          GROUP BY
            sg_session_id
        )
    ),
    event_rate AS (
      SELECT
        COUNT(
          DISTINCT CASE
            WHEN name = 'product_added_to_cart' THEN sg_session_id
          END
        ) / COUNT(DISTINCT sg_session_id) AS add_to_cart_rate,
        COUNT(
          DISTINCT CASE
            WHEN name = 'checkout_completed' THEN sg_session_id
          END
        ) / COUNT(DISTINCT sg_session_id) AS conversion_rate
      FROM
        base
    )
    SELECT
      page_viewd_standard.visitors as visitors,
      page_viewd_standard.page_views as page_views,
      session_grouped.avg_duration as avg_session_duration,
      page_viewed.bounce_rate as bounce_rate,
      non_active_page_viewed.bounce_rate as non_active_bounce_rate,
      event_rate.add_to_cart_rate as add_to_cart_rate,
      event_rate.conversion_rate as conversion_rate
    FROM
      page_viewd_standard,
      session_grouped,
      page_viewed,
      event_rate,
      non_active_page_viewed
    """))

    row = await lib.ch.query_one_dict(
        query=template.render(**params.model_dump(exclude_none=True)),
        conn_pool=conn_pool
    )
    return models.TemplateKeyMetricsOverViewData.model_validate(row)


async def query_duration_distribute(conn_pool: ChPool, params: models.TemplateQueryParams):
    template = Template(textwrap.dedent("""\
    WITH element_distribute AS (
      SELECT
        '{{traffic_source}}',
        CASE
          WHEN intersection_duration < 15000 THEN '0_15'
          WHEN intersection_duration >= 15000 AND intersection_duration < 60000 THEN '15_60'
          WHEN intersection_duration >= 60000 THEN '60_'
          ELSE ''
        END AS duration_distribute,
        sg_session_id
      FROM
        shopify.pixel_intersection_view(arg_from_timestamp = '{{ timestamp.start }}', arg_to_timestamp = '{{ timestamp.end }}', arg_shop_domain = '{{ shop_domain }}')
      WHERE
        element_tag = '{{ element_tag }}'
        AND product_id = '{{ product_id }}'
    ),
    duration_distribute_grouped AS (
      SELECT
        duration_distribute,
        COUNT(*) AS session_count
      FROM
        element_distribute
      GROUP BY
        duration_distribute
    ),
    add_to_cart_grouped AS (
      SELECT duration_distribute, COUNT(*) as session_count
      FROM element_distribute
      WHERE sg_session_id IN (SELECT DISTINCT sg_session_id FROM product_added_to_cart_view(arg_from_timestamp = '{{ timestamp.start }}', arg_to_timestamp = '{{ timestamp.end }}', arg_shop_domain = '{{ shop_domain }}'))
      GROUP BY duration_distribute
    ),
    conversion_grouped AS (
      SELECT duration_distribute, COUNT(*) as session_count
      FROM element_distribute
      WHERE sg_session_id IN (SELECT DISTINCT sg_session_id FROM checkout_completed_view(arg_from_timestamp = '{{ timestamp.start }}', arg_to_timestamp = '{{ timestamp.end }}', arg_shop_domain = '{{ shop_domain }}'))
      GROUP BY duration_distribute
    )
    SELECT
      duration_distribute_grouped.duration_distribute AS duration_distribute,
      duration_distribute_grouped.session_count AS exposed_users,
      coalesce(add_to_cart_grouped.session_count, 0) / exposed_users AS add_to_cart_rate,
      coalesce(conversion_grouped.session_count, 0) / exposed_users AS conversion_rate
    FROM
      duration_distribute_grouped
      LEFT JOIN add_to_cart_grouped ON add_to_cart_grouped.duration_distribute = duration_distribute_grouped.duration_distribute
      LEFT JOIN conversion_grouped ON conversion_grouped.duration_distribute = duration_distribute_grouped.duration_distribute
    """))
    rows = await lib.ch.query_all_dict(
        query=template.render(**params.model_dump(exclude_none=True)),
        conn_pool=conn_pool
    )
    return [models.TemplateDurationDistributeData.model_validate(row) for row in rows]


async def query_product_aggregation_analysis(conn_pool: ChPool, params: models.TemplateQueryParams):
    template = Template(textwrap.dedent("""\
    WITH base AS (
        SELECT
          coalesce(JSONExtractString(data, 'productVariant', 'product', 'id'), '') AS product_id,
          JSONExtractString(context, 'document', 'location', 'pathname') AS pathname,
          sg_session_id,
          name,
          timestamp,
          '{{ traffic_source }}'
        FROM pixel_events
        WHERE
          shop_domain = '{{ shop_domain }}'
          AND date BETWEEN toDate('{{ timestamp.start }}') AND toDate('{{ timestamp.end }}')
          AND timestamp BETWEEN '{{ timestamp.start }}' AND '{{ timestamp.end }}'
          AND JSONExtractString(context, 'navigator', 'userAgent') NOT LIKE '%Windows NT%'
          AND JSONExtractString(context, 'navigator', 'userAgent') NOT LIKE '%Macintosh%'
          AND JSONExtractString(context, 'navigator', 'userAgent') NOT LIKE '%X11%'
          AND JSONExtractString(context, 'navigator', 'userAgent') NOT LIKE '%Linux x86_64%'
          AND JSONExtractString(context, 'navigator', 'userAgent') NOT LIKE '%Electron%'
    ),
    product_grouped AS (
        SELECT
            coalesce(JSONExtractString(data, 'productVariant', 'product', 'id'), '') AS product_id,
            JSONExtractString(context, 'document', 'location', 'pathname') AS pathname,
            COUNT(DISTINCT sg_session_id) AS session_count
        FROM product_viewed_view(arg_from_timestamp = '{{ timestamp.start }}', arg_to_timestamp = '{{ timestamp.end }}', arg_shop_domain = '{{ shop_domain }}')
        GROUP BY product_id, pathname
    ),
    active_product_viewed AS (
        SELECT
            coalesce(JSONExtractString(data, 'productVariant', 'product', 'id'), '') AS product_id,
            COUNT(DISTINCT sg_session_id) AS session_count
        FROM product_viewed_view(arg_from_timestamp = '{{ timestamp.start }}', arg_to_timestamp = '{{ timestamp.end }}', arg_shop_domain = '{{ shop_domain }}')
        WHERE
            sg_session_id IN (SELECT sg_session_id FROM single_path_session_view(arg_from_timestamp='{{ timestamp.start }}', arg_to_timestamp='{{ timestamp.end }}', arg_shop_domain='{{ shop_domain }}'))
        GROUP BY product_id
    ),
    non_active_product_viewed AS (
        SELECT
            coalesce(JSONExtractString(data, 'productVariant', 'product', 'id'), '') AS product_id,
            COUNT(DISTINCT sg_session_id) AS session_count
        FROM product_viewed_view(arg_from_timestamp = '{{ timestamp.start }}', arg_to_timestamp = '{{ timestamp.end }}', arg_shop_domain = '{{ shop_domain }}')
        WHERE
            sg_session_id IN (SELECT sg_session_id FROM silence_session_view(arg_from_timestamp='{{ timestamp.start }}', arg_to_timestamp='{{ timestamp.end }}', arg_shop_domain='{{ shop_domain }}'))
        GROUP BY product_id
    ),
    add_to_cart_sessions AS (
        SELECT DISTINCT sg_session_id
        FROM product_added_to_cart_view(arg_from_timestamp = '{{ timestamp.start }}', arg_to_timestamp = '{{ timestamp.end }}', arg_shop_domain = '{{ shop_domain }}')
    ),
    add_to_cart_product_viewed AS (
        SELECT
            coalesce(JSONExtractString(data, 'productVariant', 'product', 'id'), '') AS product_id,
            COUNT(DISTINCT sg_session_id) as session_count
        FROM product_viewed_view(arg_from_timestamp = '{{ timestamp.start }}', arg_to_timestamp = '{{ timestamp.end }}', arg_shop_domain = '{{ shop_domain }}')
        WHERE
            sg_session_id IN (SELECT sg_session_id FROM add_to_cart_sessions)
        GROUP BY product_id
    ),
    conversion_sessions AS (
        SELECT DISTINCT sg_session_id
        FROM checkout_completed_view(arg_from_timestamp = '{{ timestamp.start }}', arg_to_timestamp = '{{ timestamp.end }}', arg_shop_domain = '{{ shop_domain }}')
    ),
    conversion_product_viewed AS (
        SELECT
            coalesce(JSONExtractString(data, 'productVariant', 'product', 'id'), '') AS product_id,
            COUNT(DISTINCT sg_session_id) as session_count
        FROM product_viewed_view(arg_from_timestamp = '{{ timestamp.start }}', arg_to_timestamp = '{{ timestamp.end }}', arg_shop_domain = '{{ shop_domain }}')
        WHERE
            sg_session_id IN (SELECT sg_session_id FROM conversion_sessions)
        GROUP BY product_id
    ),
    -- 下面这些是为了计算 duration
    numbered_events AS (
        SELECT
            sg_session_id,
            pathname,
            timestamp,
            ROW_NUMBER() OVER (PARTITION BY sg_session_id ORDER BY timestamp) AS rn,
            ROW_NUMBER() OVER (PARTITION BY sg_session_id, pathname ORDER BY timestamp) AS pathname_rn
        FROM base
        WHERE name='sg:heartbeat' AND pathname LIKE '/products/%'
    ),
    session_durations AS (
        SELECT
            sg_session_id, pathname, rn - pathname_rn AS group_id,
            CASE
                WHEN COUNT(*) = 1 THEN 5000  -- 只有一次heartbeat时，duration设为5秒（5000毫秒）
                ELSE dateDiff('millisecond', MIN(timestamp), MAX(timestamp))
            END AS duration_milliseconds
        FROM numbered_events
        GROUP BY sg_session_id, pathname, (rn - pathname_rn)
    ),
    path_grouped AS (
        SELECT pathname, AVG(duration_milliseconds) AS avg_duration_milliseconds
        FROM session_durations
        GROUP BY pathname
    )
    SELECT
        product_grouped.product_id AS product_id,
        product_grouped.pathname AS pathname,
        product_grouped.session_count AS exposures,
        path_grouped.avg_duration_milliseconds AS avg_duration,
        coalesce(active_product_viewed.session_count, 0) / exposures AS bounce_rate,
        coalesce(non_active_product_viewed.session_count, 0) / exposures AS non_active_bounce_rate,
        coalesce(add_to_cart_product_viewed.session_count, 0) / exposures AS add_to_cart_rate,
        coalesce(conversion_product_viewed.session_count, 0) / exposures AS conversion_rate
    FROM
        product_grouped
    LEFT JOIN active_product_viewed ON active_product_viewed.product_id=product_grouped.product_id
    LEFT JOIN non_active_product_viewed ON non_active_product_viewed.product_id=product_grouped.product_id
    LEFT JOIN add_to_cart_product_viewed ON add_to_cart_product_viewed.product_id=product_grouped.product_id
    LEFT JOIN conversion_product_viewed ON conversion_product_viewed.product_id=product_grouped.product_id
    LEFT JOIN path_grouped ON product_grouped.pathname=path_grouped.pathname
    """))
    rows = await lib.ch.query_all_dict(
        query=template.render(**params.model_dump(exclude_none=True)),
        conn_pool=conn_pool
    )
    return [models.TemplateProductAggregationData.model_validate(row) for row in rows]


async def query_product_performance(conn_pool: ChPool, params: models.TemplateQueryParams):
    template = Template(textwrap.dedent("""\
    WITH 
    -- 基础数据CTE，只查询一次
    base AS (
        SELECT
            '{{traffic_source}}' as traffic_source,
            pixel_data.element_tag,
            pixel_data.sg_event_id,
            pixel_data.sg_session_id,
            pixel_data.sg_element_id,
            pixel_data.intersection_duration,
            pixel_data.name
        FROM shopify.pixel_intersection_view(
            arg_from_timestamp = '{{ timestamp.start }}', 
            arg_to_timestamp = '{{ timestamp.end }}', 
            arg_shop_domain = '{{ shop_domain }}'
        ) AS pixel_data
        WHERE pixel_data.product_id = '{{ product_id }}'
    ),

    -- 预先筛选出所有相关的会话ID，避免重复查询
    relevant_sessions AS (
        SELECT DISTINCT sg_session_id
        FROM base
    ),

    -- 单路径会话筛选
    single_path_sessions AS (
        SELECT sg_session_id 
        FROM shopify.single_path_session_view(
            arg_from_timestamp = '{{ timestamp.start }}', 
            arg_to_timestamp = '{{ timestamp.end }}', 
            arg_shop_domain = '{{ shop_domain }}'
        )
        WHERE sg_session_id IN (SELECT sg_session_id FROM relevant_sessions)
    ),

    -- 产品查看会话
    product_viewed_sessions AS (
        SELECT DISTINCT sg_session_id
        FROM shopify.product_viewed_view(
            arg_from_timestamp = '{{ timestamp.start }}', 
            arg_to_timestamp = '{{ timestamp.end }}', 
            arg_shop_domain = '{{ shop_domain }}'
        )
        WHERE coalesce(JSONExtractString(data, 'productVariant', 'product', 'id'), '') = '{{ product_id }}'
          AND sg_session_id IN (SELECT sg_session_id FROM single_path_sessions)
    ),

    -- 添加到购物车会话
    add_to_cart_sessions AS (
        SELECT DISTINCT sg_session_id
        FROM shopify.product_added_to_cart_view(
            arg_from_timestamp = '{{ timestamp.start }}', 
            arg_to_timestamp = '{{ timestamp.end }}', 
            arg_shop_domain = '{{ shop_domain }}'
        )
        WHERE coalesce(JSONExtractString(data, 'cartLine', 'merchandise', 'product', 'id'), '') = '{{ product_id }}'
          AND sg_session_id IN (SELECT sg_session_id FROM relevant_sessions)
    ),

    -- 转化会话
    conversion_sessions AS (
        SELECT DISTINCT sg_session_id
        FROM shopify.checkout_completed_view(
            arg_from_timestamp = '{{ timestamp.start }}', 
            arg_to_timestamp = '{{ timestamp.end }}', 
            arg_shop_domain = '{{ shop_domain }}'
        )
        WHERE has(arrayMap(
            x -> JSONExtractString(x, 'variant', 'product', 'id'),
            JSONExtractArrayRaw(data, 'checkout', 'lineItems')
        ), '{{ product_id }}')
          AND sg_session_id IN (SELECT sg_session_id FROM relevant_sessions)
    ),

    -- 主要聚合查询，一次性计算所有指标
    element_metrics AS (
        SELECT
            base.sg_element_id,
            count(DISTINCT base.sg_session_id) AS exposed_users,
            sum(base.intersection_duration) AS total_intersection_duration,
            countIf(DISTINCT base.sg_session_id, base.sg_session_id IN (SELECT sg_session_id FROM product_viewed_sessions)) AS page_viewed_sessions,
            countIf(DISTINCT base.sg_session_id, base.sg_session_id IN (SELECT sg_session_id FROM add_to_cart_sessions)) AS add_to_cart_sessions,
            countIf(DISTINCT base.sg_session_id, base.sg_session_id IN (SELECT sg_session_id FROM conversion_sessions)) AS conversion_sessions
        FROM base
        GROUP BY base.sg_element_id
    )

    -- 最终结果
    SELECT
        sg_element_id AS element_id,
        exposed_users,
        if(exposed_users > 0, total_intersection_duration / exposed_users, 0) AS intersection_duration,
        if(exposed_users > 0, page_viewed_sessions / exposed_users, 0) AS bounce_rate,
        if(exposed_users > 0, add_to_cart_sessions / exposed_users, 0) AS add_to_cart_rate,
        if(exposed_users > 0, conversion_sessions / exposed_users, 0) AS conversion_rate
    FROM element_metrics
    ORDER BY exposed_users DESC;
    """))
    rows = await lib.ch.query_all_dict(
        query=template.render(**params.model_dump(exclude_none=True)),
        conn_pool=conn_pool
    )
    return [models.TemplateProductPerformanceData.model_validate(row) for row in rows]


async def query_main_image_performance(conn_pool: ChPool, params: models.TemplateQueryParams):
    template = Template(textwrap.dedent("""
    WITH 
    -- 基础数据CTE，只查询一次，并包含流量来源过滤
    base AS (
        SELECT
            pixel_data.sg_event_id,
            pixel_data.sg_session_id,
            pixel_data.sg_element_id,
            pixel_data.main_image_index, -- 针对此查询特有的主图索引
            pixel_data.intersection_duration,
            pixel_data.name,
            shopify.session_channel_view.channel AS traffic_channel -- 从 session_channel_view 获取渠道信息
        FROM shopify.pixel_intersection_view(
            arg_from_timestamp = '{{ timestamp.start }}', 
            arg_to_timestamp = '{{ timestamp.end }}', 
            arg_shop_domain = '{{ shop_domain }}'
        ) AS pixel_data
        JOIN shopify.session_channel_view ON pixel_data.sg_session_id = shopify.session_channel_view.sg_session_id
        WHERE pixel_data.element_tag = '主图' -- 过滤主图元素
          AND pixel_data.product_id = '{{ product_id }}' -- 过滤特定产品
          AND ('{{traffic_source}}' = 'all' OR shopify.session_channel_view.channel = '{{ traffic_source }}') -- 流量来源过滤
    ),

    -- 预先筛选出所有相关的会话ID，避免重复查询
    relevant_sessions AS (
        SELECT DISTINCT sg_session_id
        FROM base
    ),

    -- 单路径会话筛选 (用于 product_viewed_sessions 的额外过滤，与第一个SQL保持一致)
    single_path_sessions AS (
        SELECT sg_session_id 
        FROM shopify.single_path_session_view(
            arg_from_timestamp = '{{ timestamp.start }}', 
            arg_to_timestamp = '{{ timestamp.end }}', 
            arg_shop_domain = '{{ shop_domain }}'
        )
        WHERE sg_session_id IN (SELECT sg_session_id FROM relevant_sessions)
    ),

    -- 产品查看会话：会话中浏览了指定产品页面的会话
    product_viewed_sessions AS (
        SELECT DISTINCT sg_session_id
        FROM shopify.product_viewed_view(
            arg_from_timestamp = '{{ timestamp.start }}', 
            arg_to_timestamp = '{{ timestamp.end }}', 
            arg_shop_domain = '{{ shop_domain }}'
        )
        WHERE coalesce(JSONExtractString(data, 'productVariant', 'product', 'id'), '') = '{{ product_id }}'
          AND sg_session_id IN (SELECT sg_session_id FROM single_path_sessions) -- 保持与第一个SQL的逻辑一致
    ),

    -- 添加到购物车会话：会话中将指定产品添加到购物车的会话
    add_to_cart_sessions AS (
        SELECT DISTINCT sg_session_id
        FROM shopify.product_added_to_cart_view(
            arg_from_timestamp = '{{ timestamp.start }}', 
            arg_to_timestamp = '{{ timestamp.end }}', 
            arg_shop_domain = '{{ shop_domain }}'
        )
        WHERE coalesce(JSONExtractString(data, 'cartLine', 'merchandise', 'product', 'id'), '') = '{{ product_id }}'
          AND sg_session_id IN (SELECT sg_session_id FROM relevant_sessions)
    ),

    -- 转化会话：会话中完成了包含指定产品订单的会话
    conversion_sessions AS (
        SELECT DISTINCT sg_session_id
        FROM shopify.checkout_completed_view(
            arg_from_timestamp = '{{ timestamp.start }}', 
            arg_to_timestamp = '{{ timestamp.end }}', 
            arg_shop_domain = '{{ shop_domain }}'
        )
        WHERE has(arrayMap(
            x -> JSONExtractString(x, 'variant', 'product', 'id'),
            JSONExtractArrayRaw(data, 'checkout', 'lineItems')
        ), '{{ product_id }}')
          AND sg_session_id IN (SELECT sg_session_id FROM relevant_sessions)
    ),

    -- 主要聚合查询，一次性计算所有指标，按 main_image_index 分组
    image_metrics AS (
        SELECT
            base.main_image_index,
            count(DISTINCT base.sg_session_id) AS exposed_users, -- 曝光用户数
            sum(base.intersection_duration) AS total_intersection_duration, -- 总曝光时长
            -- 计算浏览产品页面的会话数（遵循第一个SQL的逻辑）
            count(DISTINCT IF(base.sg_session_id IN (SELECT sg_session_id FROM product_viewed_sessions), base.sg_session_id, NULL)) AS product_viewed_sessions_count,
            -- 计算添加到购物车的会话数
            count(DISTINCT IF(base.sg_session_id IN (SELECT sg_session_id FROM add_to_cart_sessions), base.sg_session_id, NULL)) AS add_to_cart_sessions_count,
            -- 计算转化会话数
            count(DISTINCT IF(base.sg_session_id IN (SELECT sg_session_id FROM conversion_sessions), base.sg_session_id, NULL)) AS conversion_sessions_count
        FROM base
        GROUP BY base.main_image_index
    )

    -- 最终结果
    SELECT
        main_image_index,
        exposed_users,
        if(exposed_users > 0, total_intersection_duration / exposed_users, 0) AS intersection_duration, -- 平均曝光时长
        -- 这里的 bounce_rate 实际上是产品页面浏览率，与第一个SQL的计算方式保持一致
        if(exposed_users > 0, product_viewed_sessions_count / exposed_users, 0) AS bounce_rate, 
        if(exposed_users > 0, add_to_cart_sessions_count / exposed_users, 0) AS add_to_cart_rate,
        if(exposed_users > 0, conversion_sessions_count / exposed_users, 0) AS conversion_rate
    FROM image_metrics
    ORDER BY exposed_users DESC;
    """))
    rows = await lib.ch.query_all_dict(
        query=template.render(**params.model_dump(exclude_none=True)),
        conn_pool=conn_pool
    )
    return [models.TemplateMainImageData.model_validate(row) for row in rows]


async def query_product_order(conn_pool: ChPool, shop_domain: str, time_range):
    """product 按照曝光排序"""
    template = Template(textwrap.dedent("""\
    SELECT
        coalesce(JSONExtractString(data, 'productVariant', 'product', 'id'), '') AS product_id
    FROM pixel_events
    WHERE
        shop_domain = '{{ shop_domain }}'
        AND date BETWEEN toDate('{{ time_range.low }}') AND toDate('{{ time_range.high }}')
        AND timestamp BETWEEN '{{ time_range.low }}' AND '{{ time_range.high }}'
        AND name = 'product_viewed'
        AND JSONExtractString(context, 'navigator', 'userAgent') NOT LIKE '%Windows NT%' 
        AND JSONExtractString(context, 'navigator', 'userAgent') NOT LIKE '%Macintosh%'
        AND JSONExtractString(context, 'navigator', 'userAgent') NOT LIKE '%X11%'
        AND JSONExtractString(context, 'navigator', 'userAgent') NOT LIKE '%Linux x86_64%' 
        AND JSONExtractString(context, 'navigator', 'userAgent') NOT LIKE '%Electron%'
    GROUP BY product_id
    ORDER BY COUNT(DISTINCT sg_session_id) DESC;
    """))
    rows: list[str] = await lib.ch.query_all_scalar(
        query=template.render(shop_domain=shop_domain, time_range=time_range),
        conn_pool=conn_pool
    )
    return rows

class TemplateLoader:
    def __init__(self, template_dir: Path):
        self.env = Environment(
            loader=FileSystemLoader(str(template_dir)),
            autoescape=False,
            trim_blocks=True,
            lstrip_blocks=True,
        )

    def render_query(self, template_name: str,
                     params: dict[str, Any] = None) -> str:
        """
        加载指定图表名称的模板，并渲染参数
        chart_name: 不含扩展名的模板名，如 'user_event' -> user_event.sql.j2
        """
        template_file = f"{template_name}.sql.j2"
        try:
            template = self.env.get_template(template_file)
        except TemplateNotFound:
            raise ValueError(f"Chart template '{template_name}' not found")

        return template.render(**(params or {}))


template_loader = TemplateLoader(Path(__file__).resolve().parent / "sql")


class PixelEvents:
    """原始事件"""
    table = exp.Table(this=exp.to_identifier("pixel_events"))
    sg_event_id = exp.Column(this="sg_event_id", table=table.alias)
    sg_new_client_id = exp.Column(this="sg_new_client_id", table=table.alias)
    sg_session_id = exp.Column(this="sg_session_id", table=table.alias)
    name = exp.Column(this="name", table=table.alias)
    type = exp.Column(this="type", table=table.alias)
    shop_domain = exp.Column(this="shop_domain")
    date = exp.Column(this="date")
    timestamp = exp.Column(this="timestamp")

    product_id = raw("JSONExtractString(data, 'sgExtraData', 'product_id')")

    @classmethod
    def time_range_to_sql(cls, time_range: models.TimeRangeValue):
        from . import condition_builder
        return lib.ch.to_sql(
            condition_builder.filter_value(time_range, cls.timestamp,
                                           date_column=cls.date))

