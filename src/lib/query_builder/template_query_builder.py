import asyncio

from sqlmodel.ext.asyncio.session import AsyncSession

from src import models, lib
from src.utils import safe_number_str, TimeRangeHelper


class TemplateQueryBuilder:

    def __init__(self, shop_domain: str, query_context: models.TemplateQueryContext):
        self.shop_domain = shop_domain
        self.query_context = query_context

    async def key_metrics_overview(self):
        params = lib.query_builder.template.query_context_to_template_params(
            shop_domain=self.shop_domain, query_context=self.query_context
        )
        ratio_time_range = TimeRangeHelper.get_ratio(self.query_context.global_filters.time_range)
        ratio_params = params.model_copy(
            update={"timestamp": {"start": ratio_time_range.low, "end": ratio_time_range.high}}
        )

        async with asyncio.TaskGroup() as tg:
            current_data_task = tg.create_task(
                lib.query_builder.template.query_key_metric_overview(conn_pool=lib.ch.pool, params=params))
            last_period_data_task = tg.create_task(
                lib.query_builder.template.query_key_metric_overview(conn_pool=lib.ch.pool, params=ratio_params))
        current_data = current_data_task.result()
        last_period_data = last_period_data_task.result()

        response = models.TemplateKeyMetricsOverViewResponse.model_validate(current_data,
                                                                            update=last_period_data.to_last_period())
        return [response]

    async def product_performance(self, knowledge_db_session: AsyncSession):
        params = lib.query_builder.template.query_context_to_template_params(
            shop_domain=self.shop_domain,
            query_context=self.query_context
        )
        query_results = await lib.query_builder.template.query_product_performance(conn_pool=lib.ch.pool, params=params)
        data = []
        for query_result in query_results:
            response_data = models.TemplateProductPerformanceResponse(
                element_id=query_result.element_id,
                element_name="",
                exposures=query_result.exposed_users,
                avg_duration=query_result.intersection_duration,
                bounce_rate=query_result.bounce_rate,
                add_to_cart=query_result.add_to_cart_rate,
                conversion=query_result.conversion_rate,
                insights=[models.InsightInfo(
                    insight_type="HIGH_VALUE_SELLING_POINT_SHORT_OF_EXPOSURE",
                    insight_data={
                        "last_n_day": 7,  # 最近N天
                        "avg_duration": 5000,  # 元素平均停留时长
                        "add_cart_rate_with_exposure": 0.5,  # 曝光元素的加购率
                        "add_cart_rate": 0.1,  # 元素所在商详页的加购率
                        "conversion_with_exposure": 0.5,  # 曝光元素的下单转化率
                        "conversion_rate": 0.1,  # 元素所在商详页的下单转化率
                        "screen_index": 3,  # 卖点再第几屏
                        "visitors": 10,  # 商详页访客数
                        "visitors_exposure": 3  # 看过元素曝光的访客数
                    }
                )],
            )
            data.append(response_data)
        element_map = {row.element_id: row for row in data}
        element_name_wrapper = await lib.knowledge.ElementWrapper.init_by_element_ids(knowledge_db_session,
                                                                                      list(element_map.keys()))

        for row in data:
            element_name_wrapper.apply_template_page_element(row)

        return [row for row in data if row.element_name != row.element_id]

    async def all_product_aggregation_analysis(self, store_center_db_session: AsyncSession):
        params = lib.query_builder.template.query_context_to_template_params(
            shop_domain=self.shop_domain,
            query_context=self.query_context
        )
        query_results = await lib.query_builder.template.query_product_aggregation_analysis(conn_pool=lib.ch.pool,
                                                                                            params=params)
        data = []
        spu_wrapper = await lib.store_center.SPUWrapper.init_by_shop_domain(session=store_center_db_session,
                                                                            shop_domain=self.shop_domain)
        for query_result in query_results:
            response_data = models.TemplateProductAggregationResponse(
                product_id=query_result.product_id,
                product_name=spu_wrapper.get_product_name(query_result.product_id),
                exposures=query_result.exposures,
                avg_duration=query_result.avg_duration,
                bounce_rate=query_result.bounce_rate,
                non_active_bounce_rate=query_result.non_active_bounce_rate,
                add_to_cart=query_result.add_to_cart_rate,
                conversion=query_result.conversion_rate,
                product_path=query_result.pathname
            )
            data.append(response_data)

        return sorted(data, key=lambda row: int(row.exposures), reverse=True)

    async def main_image_performance(self):
        params = lib.query_builder.template.query_context_to_template_params(
            shop_domain=self.shop_domain,
            query_context=self.query_context
        )
        query_results = await lib.query_builder.template.query_main_image_performance(conn_pool=lib.ch.pool,
                                                                                      params=params)
        data = []
        for query_result in query_results:
            response_data = models.TemplateMainImageResponse(
                main_image_index=query_result.main_image_index,
                image_number="主图-" + query_result.main_image_index,
                exposed_users=query_result.exposed_users,
                avg_duration=query_result.intersection_duration,
                bounce_rate=query_result.bounce_rate,
                add_to_cart=query_result.add_to_cart_rate,
                conversion=query_result.conversion_rate
            )
            data.append(response_data)
        return sorted(data, key=lambda row: int(row.exposed_users), reverse=True)

    async def duration_range_analysis(self):
        params = lib.query_builder.template.query_context_to_template_params(
            shop_domain=self.shop_domain,
            query_context=self.query_context
        )
        query_results = await lib.query_builder.template.query_duration_distribute(conn_pool=lib.ch.pool, params=params)

        data_map = {
            "0-15s": models.TemplateDurationDistributeResponse(
                duration_range="0-15s",
                exposed_users="0",
                add_to_cart="0",
                conversion="0"
            ),
            "15-60s": models.TemplateDurationDistributeResponse(
                duration_range="15-60s",
                exposed_users="0",
                add_to_cart="0",
                conversion="0"
            ),
            ">60s": models.TemplateDurationDistributeResponse(
                duration_range=">60s",
                exposed_users="0",
                add_to_cart="0",
                conversion="0"
            )
        }
        for query_result in query_results:
            duration_range = {"0_15": "0-15s", "15_60": "15-60s", "60_": ">60s"}[query_result.duration_distribute]
            data_map[duration_range].exposed_users = safe_number_str(query_result.exposed_users)
            data_map[duration_range].add_to_cart = safe_number_str(query_result.add_to_cart_rate)
            data_map[duration_range].conversion = safe_number_str(query_result.conversion_rate)

        return [data_map["0-15s"], data_map["15-60s"], data_map[">60s"]]
