import httpx
from pydantic import BaseModel
from sqlalchemy.ext.asyncio import create_async_engine
from sqlalchemy.orm import sessionmaker
from sqlmodel import select
from sqlmodel.ext.asyncio.session import AsyncSession

from src import models
from src.config import settings

engine = create_async_engine(settings.SHOPIFY_KNOWLEDGE_DB_URL)
get_session = sessionmaker(bind=engine, class_=AsyncSession, expire_on_commit=False)


async def batch_get_page_elements(session: AsyncSession, element_ids: list[str]) -> list[models.PageElement]:
    statement = select(models.PageElement).where(models.PageElement.target.in_(element_ids))
    result = await session.exec(statement)
    return result.all()


async def batch_get_products(session: AsyncSession, product_ids: list[str]) -> list[models.ProductMetadata]:
    statement = select(models.ProductMetadata).where(models.ProductMetadata.product_id.in_(product_ids))
    result = await session.exec(statement)
    return result.all()


async def list_domain_products(session: AsyncSession, shop_domain: str) -> list[models.ProductMetadata]:
    statement = select(models.ProductMetadata).where(models.ProductMetadata.store_domain == shop_domain)
    result = await session.exec(statement)
    return result.all()


class ElementWrapper:
    def __init__(self, page_elements: list[models.PageElement]):
        self.page_elements = page_elements
        self.page_element_map: dict[str, models.PageElement] = dict()
        self.page_element_tag_group: dict[str, list[models.PageElement]] = dict()
        for page_element in page_elements:
            self.page_element_map[page_element.target] = page_element
            self.page_element_tag_group.setdefault(page_element.data.element_tag, []).append(page_element)

    @classmethod
    async def init_by_element_ids(cls, session: AsyncSession, element_ids):
        page_elements = await batch_get_page_elements(session, element_ids)
        return cls(page_elements)

    def apply_template_page_element(self, row: models.TemplateProductPerformanceResponse):
        if row.element_id not in self.page_element_map:
            row.element_name = row.element_id
            return
        page_element = self.page_element_map[row.element_id]
        element_tag = page_element.data.element_tag
        if element_tag == models.ElementTagEnum.MAIN_IMAGE:
            element_name = "主图-{}".format(page_element.data.main_image_index or "[deprecated]")
        elif element_tag == models.ElementTagEnum.SELLER_POINTS:
            element_name = "商品卖点-{}".format(page_element.data.selling_point_summary)
        else:
            if len(self.page_element_tag_group[element_tag]) == 1:
                element_name = element_tag
            else:
                index = self.page_element_tag_group[element_tag].index(page_element) + 1
                element_name = "{}{}".format(element_tag, index)
        row.element_name = element_name
        row.element_info.selectors = page_element.selectors
        row.element_info.main_image_url = page_element.data.main_image_url


class ProductWrapper:
    def __init__(self, products: list[models.ProductMetadata]):
        self.products = products
        self.product_map = {product.product_id: product for product in products}
        self.product_path_map = {
            self.get_product_path(product.product_url): product
            for product in products
        }

    @classmethod
    async def init_by_product_ids(cls, session: AsyncSession, product_ids: list[str]):
        products = await batch_get_products(session, product_ids)
        return cls(products)

    @classmethod
    async def init_by_shop_domain(cls, session: AsyncSession, shop_domain: str):
        products = await list_domain_products(session, shop_domain)
        return cls(products)

    def apply_template_product(self, row: models.TemplateProductAggregationData):
        if row.product_id not in self.product_map:
            row.product_name = row.product_id
            return
        product = self.product_map[row.product_id]
        row.product_name = product.product_title

    def get_product_by_product_path(self, product_path: str) -> models.ProductMetadata | None:
        return self.product_path_map.get(product_path)

    @staticmethod
    def get_product_path(product_url: str):
        from urllib.parse import urlparse
        return urlparse(product_url).path


class ElementAssociationPolicyOverview(BaseModel):
    pageElementTarget: str
    policyCount: int
    enablePolicyCount: int


class ElementAssociationPolicyOverviewResp(BaseModel):
    """
    {
    "code": 0,
    "msg": "string",
    "data": {
        "pageElementAssociationPolicyOverviewList": [
            {
                "pageElementTarget": "string",
                "policyCount": 0,
                "enablePolicyCount": 0
            }
        ]
    }
}

    """

    class Data(BaseModel):
        pageElementAssociationPolicyOverviewList: list[ElementAssociationPolicyOverview]

    code: int
    msg: str
    data: Data


async def element_association_policy_overview(product_id: str,
                                              element_ids: list[str]) -> ElementAssociationPolicyOverviewResp:
    response = await _client.post(
        "guidePolicy/v1/pageElementAssociationPolicyOverview",
        json={
            "spuId": product_id,
            "pageElementTargets": element_ids
        }
    )
    response.raise_for_status()
    return ElementAssociationPolicyOverviewResp.model_validate(response.json())


_client = httpx.AsyncClient(base_url="http://web.shopify-knowledge.lain:8080/shopify-knowledge")
