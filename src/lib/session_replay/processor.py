import asyncio
from datetime import datetime, UTC
from loguru import logger
from typing import List


from . import get_session
from .cos_client import cos_client
from .session_replay import SessionReplay


class ReplayMessage:
    def __init__(self, record) -> None:
        self.record = record
        with logger.contextualize(**self.record_to_db_data()):
            logger.info("保存replay消息")

    @property
    def session_id(self):
        return self.record["sg_session_id"]

    @property
    def replay_id(self):
        return self.record["data"].get("replay_id")

    @property
    def shop_domain(self):
        return self.record.get("shop_domain")

    @property
    def timestamp(self):
        return self.record.get("timestamp", datetime.now(UTC).isoformat(sep="T", timespec='milliseconds').replace("+00:00", "Z"))

    def validate(self):
        return self.session_id and self.replay_id and self.shop_domain

    def record_to_db_data(self):
        return {"sg_session_id": self.session_id, "replay_id": self.replay_id, "shop_domain": self.shop_domain}

    def cos_path(self) -> str:
        return f"{self.shop_domain}/{self.replay_id}/{self.session_id}_{self.timestamp}.json"

    def record_to_cos(self):
        return {"cos_key": self.cos_path(), "data": self.record}

    async def save(self, session):
        await SessionReplay.batch_save_replays(session, [self.record_to_db_data()])
        await cos_client.upload_data(self.cos_path(), self.record)


class ReplayProcessor:
    REPLAY_NAME = "sg:replay-events"
    topic = "shopify-web-pixel-event"

    def valid(self, record: dict):
        return record.get("name", "") == self.REPLAY_NAME

    def accept_records(self, records) -> List[ReplayMessage]:
        return [ReplayMessage(record) for record in records if self.valid(record)]

    async def process(self, records):
        messages = self.accept_records(records)
        if messages:
            await self.save_messages(messages)

    async def save_messages(self, messages: List[ReplayMessage]):
        await self._save_db(messages)
        results = await self._save_cos(messages)
        logger.info(f"备份文件到cos数量: {len(results)}")

    async def _save_db(self, messages: List[ReplayMessage]) -> None:
        datas = {(message.session_id, message.replay_id): message.record_to_db_data() for message in messages if message.validate()}
        if not datas:
            return
        async with get_session() as session:
            await SessionReplay.batch_save_replays(session, list(datas.values()))

    async def _save_cos(self, messages: List[ReplayMessage]) -> list:
        async with asyncio.TaskGroup() as tg:
            return [
                tg.create_task(cos_client.upload_data(**message.record_to_cos()))
                for message in messages
                if message.validate()
            ]
