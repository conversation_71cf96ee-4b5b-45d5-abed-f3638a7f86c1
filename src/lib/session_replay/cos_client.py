import io
from loguru import logger
import tempfile
import json
import asyncio
from qcloud_cos import CosConfig, CosS3Client
from src.config import settings


class CosClient:
    BYTES_SIZE_LIMIT = 1024 * 1024

    def __init__(self) -> None:
        config = CosConfig(
            Region=settings.COS_REGION,
            SecretId=settings.COS_SECRET_ID,
            SecretKey=settings.COS_SECRET_KEY,
        )
        self.client = CosS3Client(config)
        self.bucket = settings.COS_BUCKET

    def _replay_keys(self, prefix):
        response = self.client.list_objects(
            Bucket=self.bucket,
            Prefix=prefix
        )
        if 'Contents' in response:
            return [
                content["Key"]
                for content in response['Contents']
            ]
        return []

    async def replay_keys(self, prefix):
        loop = asyncio.get_event_loop()
        return await loop.run_in_executor(None, self._replay_keys, prefix)

    def _get_presigned_url(self, key):
        return self.client.get_presigned_download_url(
            Bucket=self.bucket,
            Key=key,
            Expired=300
        )

    def _upload_big_data(self, key, data: bytes) -> None:
        with tempfile.NamedTemporaryFile(mode="wb", suffix=".json") as f:
            f.write(data)
            f.flush()
            response = self.client.upload_file(
                Bucket=self.bucket,
                LocalFilePath=f.name,
                Key=key
            )
            return response["ETag"]

    def _upload_small_data(self, key, data: bytes) -> None:
        response = self.client.put_object(self.bucket, data, key)
        return response["ETag"]

    async def upload_data(self, cos_key, data):
        if isinstance(data, dict):
            data = json.dumps(data, ensure_ascii=False).encode(encoding="utf-8")
        loop = asyncio.get_event_loop()
        if len(data) < self.BYTES_SIZE_LIMIT:
            result = await loop.run_in_executor(None, self._upload_small_data, cos_key, data)
        else:
            result = await loop.run_in_executor(None, self._upload_big_data, cos_key, data)
        logger.info(f"上传文件到oss：{cos_key}, response: {result}")

    async def get_presigned_url(self, key):
        loop = asyncio.get_event_loop()
        return await loop.run_in_executor(None, self._get_presigned_url, key)

    async def get_replay_download_urls(self, prefix):
        keys = await self.replay_keys(prefix)
        keys.sort()
        return prefix, await asyncio.gather(
            *[
                self.get_presigned_url(key)
                for key in keys
            ]
        )

    async def batch_replay_urls(self, prefixes):
        replay_download_urls = await asyncio.gather(
            *[
                self.get_replay_download_urls(prefix)
                for prefix in prefixes
            ]
        )
        return {
            prefix: download_urls
            for (prefix, download_urls) in replay_download_urls
        }


cos_client = CosClient()
