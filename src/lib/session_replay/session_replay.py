from loguru import logger
from datetime import datetime, UTC
from sqlalchemy.dialects import postgresql as pg
from sqlmodel.ext.asyncio.session import AsyncSession
from sqlmodel import SQLModel, Field
from sqlmodel import select


class SessionReplay(SQLModel, table=True):
    __tablename__ = "session_replays"
    sg_session_id: str = Field(primary_key=True)
    replay_id: str = Field(primary_key=True)
    shop_domain: str
    sg_new_client_id: str | None = None

    def oss_prefix(self):
        return f"{self.shop_domain}/{self.replay_id}"

    def oss_key(self):
        date_str = datetime.now(UTC).isoformat(sep="T", timespec='milliseconds').replace("+00:00", "Z")
        return f"{self.oss_prefix()}/{self.sg_session_id}_{date_str}.json"

    def is_valid(self):
        return self.sg_session_id and self.replay_id and self.shop_domain

    @classmethod
    async def get_session_replays(cls, session: AsyncSession, session_id: str):
        stmt = select(
            SessionReplay
        ).where(
            SessionReplay.sg_session_id == session_id
        )
        result = await session.exec(stmt)
        return result.all()

    @classmethod
    async def batch_save_replays(cls, session: AsyncSession, session_replays):
        """批量数据获取"""
        stmt = pg.insert(
            SessionReplay
        ).values(
            session_replays
        ).on_conflict_do_nothing()
        await session.exec(stmt)
        await session.commit()
