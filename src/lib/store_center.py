from sqlalchemy.ext.asyncio import create_async_engine
from sqlalchemy.orm import sessionmaker
from sqlmodel import select
from sqlmodel.ext.asyncio.session import AsyncSession

from src import models
from src.config import settings

engine = create_async_engine(settings.STORE_CENTER_DB_URL)
get_session = sessionmaker(bind=engine, class_=AsyncSession, expire_on_commit=False)


async def list_domain_spus(session: AsyncSession, shop_domain: str) -> list[models.SPU]:
    statement = select(models.SPU).where(models.SPU.domain == shop_domain)
    result = await session.exec(statement)
    return result.all()


class SPUWrapper:
    def __init__(self, spu_list: list[models.SPU]):
        self.spu_list = spu_list
        self.spu_map = {spu.spu_id: spu for spu in spu_list}

    @classmethod
    async def init_by_shop_domain(cls, session: AsyncSession, shop_domain: str):
        spu_list = await list_domain_spus(session, shop_domain)
        return cls(spu_list=spu_list)

    def get_product_name(self, product_id: str):
        if product_id not in self.spu_map:
            return product_id
        else:
            return self.spu_map[product_id].spu_name
