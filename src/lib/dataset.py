"""

## 物理列

表的结构信息（列名、数据类型等）。会在 `TableColumn` 中存在一个对应的记录，且 `expression` 为空。

## 用户自定义

* 修改标签: 编辑 `label`
* 修改描述: 编辑 `description`
* 创建计算列: 添加一个新的 `TableColumn` 记录，其中 `expression` 必填，而不是指向一个已有的物理列
* 创建指标: 添加一条 `SQLMetric` 记录，其中 `expression` 必填

## 虚拟数据集

对于图表制作者来说，他们看到的不是原始的数据库表，而是一个维护的“虚拟数据集”。这个虚拟数据集包含了：
* 所有物理列（以及它们的自定义标签和描述）。
* 所有用户定义的计算列。
* 所有用户定义的指标。

## 查询生成

当用户在创建自定义图表时，查询生成器会智能地利用这些元数据
* 如果用户选择了一个物理列，查询语句会直接使用 `column_name`
* 如果用户选择了一个计算列，查询语句会将其 `expression` 嵌入到 SELECT 或 GROUP BY 子句中
* 如果用户选择了一个指标，查询语句会将其 `expression` 嵌入到聚合部分

"""
