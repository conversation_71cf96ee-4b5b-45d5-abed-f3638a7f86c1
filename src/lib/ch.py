"""clickhouse"""
import contextlib

from asynch import Pool, Connection
from asynch.cursors import <PERSON>urs<PERSON>, DictCursor
from sqlglot import exp

from src.config import settings

pool = Pool(minsize=1, maxsize=24, dsn=settings.CLICKHOUSE_URL)


def to_sql(query: exp.Expression | str):
    """工具方法，用于将 sqlglot 的表达式或 sql string 转换成可以用于 asynch cursor 查询使用的 SQL"""
    if isinstance(query, exp.Expression):
        return _generator.generate(query)
    return str(query)


@contextlib.asynccontextmanager
async def get_cursor(cursor: type[Cursor] | None = None, conn_pool: Pool | None = None, conn: Connection | None = None):
    if conn:
        async with conn.cursor(cursor) as cursor:
            yield cursor
    elif conn_pool:
        async with conn_pool.connection() as conn, conn.cursor(cursor) as cursor:
            yield cursor
    else:
        raise ValueError("need conn or conn_pool")


async def query_one_dict(query: str, conn_pool: Pool | None = None, conn: Connection | None = None) -> dict:
    async with get_cursor(DictCursor, conn_pool=conn_pool, conn=conn) as cursor:
        await cursor.execute(to_sql(query))
        return await cursor.fetchone()


async def query_all_dict(query: str, conn_pool: Pool | None = None, conn: Connection | None = None) -> dict:
    async with get_cursor(DictCursor, conn_pool=conn_pool, conn=conn) as cursor:
        await cursor.execute(to_sql(query))
        return await cursor.fetchall()


async def query_one_scalar(query: str, conn_pool: Pool | None = None, conn: Connection | None = None):
    async with get_cursor(conn_pool=conn_pool, conn=conn) as cursor:
        await cursor.execute(to_sql(query))
        return (await cursor.fetchone())[0]


async def query_all_scalar(query: str, conn_pool: Pool | None = None, conn: Connection | None = None):
    async with get_cursor(conn_pool=conn_pool, conn=conn) as cursor:
        await cursor.execute(to_sql(query))
        return [row[0] for row in await cursor.fetchall()]


def _get_ch_generator():
    from sqlglot import exp
    from sqlglot import Generator
    from sqlglot.dialects.dialect import unit_to_str
    from src.config import settings

    class CustomGenerator(Generator):
        def datediff_sql(self, expression: exp.DateDiff):
            return self.func(
                "dateDiff",
                unit_to_str(expression),
                expression.expression,
                expression.this,
            )

        def jsonextractscalar_sql(self, expression):
            return self.func(
                expression.meta["name"],
                expression.this,
                *["'" + path.this + "'" for path in expression.expression.expressions[1:]]
            )

    return CustomGenerator(dialect="clickhouse", pretty=settings.ENVIRONMENT == "local")


_generator = _get_ch_generator()
