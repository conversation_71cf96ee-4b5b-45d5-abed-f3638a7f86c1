from typing import Annotated

from asynch import Pool as ChPool
from fastapi import APIRouter, Depends, HTTPException
from pydantic import BaseModel
from sqlmodel.ext.asyncio.session import AsyncSession

from src import models, lib
from src.shop_bi import deps
from src.utils import ALL_COUNTRY_CODE

router = APIRouter(prefix="/bi", tags=["报表数据查询"], dependencies=[Depends(deps.get_current_user)])

class TemplateOptionsResponse(BaseModel):
    options: dict[str, list[models.Option]]


@router.get("/template-options")
async def get_options() -> TemplateOptionsResponse:
    return TemplateOptionsResponse(
        options={
            "user_type": models.TemplateQueryUserTypeEnum.get_options(),
            "traffic_source": models.TemplateQueryTrafficSourceEnum.get_options(),
            "user_location": [models.Option(label=code, value=ALL_COUNTRY_CODE[code]) for code in ALL_COUNTRY_CODE],
            "element_tag": models.ElementTagEnum.get_options(),
        }
    )


class TemplateProductsResponse(BaseModel):
    products: list[models.ProductTitleUrl]


@router.post("/template-products")
async def get_options_and_products(
        shop_domain: Annotated[str, Depends(deps.shop_domain)],
        query_context: models.TemplateQueryContext,
        ch_pool: Annotated[ChPool, Depends(deps.get_ch_pool)],
        knowledge_db_session: Annotated[AsyncSession, Depends(deps.get_knowledge_db_session)],
) -> TemplateProductsResponse:
    product_id_order = await lib.query_builder.template.query_product_order(
        conn_pool=ch_pool, shop_domain=shop_domain, time_range=query_context.global_filters.time_range
    )
    products = await lib.knowledge.list_domain_products(session=knowledge_db_session, shop_domain=shop_domain)
    order_map = {pid: i for i, pid in enumerate(product_id_order)}
    products.sort(key=lambda p: order_map.get(p.product_id, float("inf")))
    response = TemplateProductsResponse(
        products=[models.ProductTitleUrl.model_validate(product) for product in products]
    )
    return response


class QueryTemplateDataResponse(BaseModel):
    data: list


async def get_template_query_builder(shop_domain: Annotated[str, Depends(deps.shop_domain)],):


@router.post("/templates/site/key-metrics-overview")
async def site_key_metrics_overview(
        query_context: models.TemplateQueryContext,
        shop_domain: Annotated[str, Depends(deps.shop_domain)],
        knowledge_db_session: Annotated[AsyncSession, Depends(deps.get_knowledge_db_session)],
        store_center_db_session: Annotated[AsyncSession, Depends(deps.get_store_center_db_session)],
):



@router.post("/templates/{template_name}")
async def query_template_data(
        template_name: str,
        query_context: models.TemplateQueryContext,
        shop_domain: Annotated[str, Depends(deps.shop_domain)],
        knowledge_db_session: Annotated[AsyncSession, Depends(deps.get_knowledge_db_session)],
        store_center_db_session: Annotated[AsyncSession, Depends(deps.get_store_center_db_session)],
) -> QueryTemplateDataResponse:
    builder = lib.query_builder.TemplateQueryBuilder(shop_domain=shop_domain, query_context=query_context)
    match template_name:
        case "key-metrics-overview":
            data = [row.model_dump() for row in await builder.key_metrics_overview()]
        case "product-performance":
            if query_context.product_id is None:
                raise HTTPException(400, detail="缺少 product_id 参数")
            data = [row.model_dump() for row in
                    await builder.product_performance(knowledge_db_session=knowledge_db_session)]
        case "product-aggregation-analysis":
            data = [row.model_dump() for row in
                    await builder.all_product_aggregation_analysis(store_center_db_session=store_center_db_session)]
        case "main-image-performance":
            data = [row.model_dump() for row in
                    await builder.main_image_performance()]
        case "duration-range-analysis":
            if query_context.element_tag is None:
                raise HTTPException(400, detail="缺少 element_tag 参数")
            data = [row.model_dump() for row in await builder.duration_range_analysis()]
        case _:
            data = []
    return QueryTemplateDataResponse(data=data)
