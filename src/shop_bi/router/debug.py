import json

import jwt
from asynch import Pool as ChPool
from fastapi import APIRouter, Request, Form, Depends
from fastapi.responses import HTMLResponse
from fastapi.templating import Jinja2Templates

from src import models, lib
from src.shop_bi import deps

router = APIRouter(tags=["DEBUG工具"])
templates = Jinja2Templates(directory="src/web/templates")


@router.get("/debug", response_class=HTMLResponse)
async def get_debug_page(request: Request):
    return templates.TemplateResponse("debug.html", {"request": request, "results": None})


def parse_curl_command(curl_command):
    import shlex
    tokens = shlex.split(curl_command)
    parsed_data = {
        "url": None,
        "method": "GET",
        "headers": {},
        "data": None,
    }
    i = 0
    while i < len(tokens):
        token = tokens[i]
        if token == '-X' or token == '--request':
            parsed_data['method'] = tokens[i + 1]
            i += 2
        elif token == '-H' or token == '--header':
            header_str = tokens[i + 1]
            key, value = header_str.split(':', 1)
            parsed_data['headers'][key.strip()] = value.strip()
            i += 2
        elif token == '-d' or token == '--data' or token == '--data-raw':
            parsed_data['data'] = tokens[i + 1]
            i += 2
        elif token.startswith('http'):
            parsed_data['url'] = token
            i += 1
        else:
            i += 1
    return parsed_data


def parse_curl_info(curl_command):
    parsed = parse_curl_command(curl_command)
    url_match = parsed["url"]
    assert url_match, "Invalid curl command"
    data_match = json.loads(parsed["data"])
    assert data_match, "Invalid curl command"
    template_name = url_match.split('/')[-1]
    token = None
    for header, value in parsed["headers"].items():
        if header.lower() == "authorization":
            token = value.split(' ')[-1]
    assert token, "Authorization token not found"
    query_context = models.TemplateQueryContext.model_validate(data_match)
    payload_obj = jwt.decode(token, algorithms=["HS256"], options={"verify_signature": False})
    jwt_payload = models.JWTPayload.model_validate(payload_obj)
    shop_domain = jwt_payload.storeInfo.domain
    return {
        "shop_domain": shop_domain,
        "query_context": query_context,
        "template_name": template_name,
        "token": token
    }


@router.post("/debug/sql-parser", response_class=HTMLResponse)
async def debug_sql_parser(
        request: Request,
        curl_command: str = Form(...),
):
    try:
        parsed = parse_curl_info(curl_command)
    except Exception as e:
        return templates.TemplateResponse(
            "debug.html",
            {"request": request, "error": str(e), "curl_command": curl_command}
        )
    shop_domain = parsed["shop_domain"]
    query_context = parsed["query_context"]
    template_name = parsed["template_name"]
    match template_name:
        case "key-metrics-overview":
            query_id = models.TemplateKeyMetricsOverViewData.QUERY_ID
            query_params = lib.query_builder.template.query_context_to_template_params(shop_domain, query_context).model_dump(exclude={"product_td", "element_tag"})
        case "product-aggregation-analysis":
            query_id = models.TemplateProductAggregationData.QUERY_ID
            query_params = lib.query_builder.template.query_context_to_template_params(shop_domain, query_context).model_dump(exclude={"product_td", "element_tag"})
        case "product-performance":
            query_id = models.TemplateProductPerformanceData.QUERY_ID
            query_params = lib.query_builder.template.query_context_to_template_params(shop_domain, query_context).model_dump(exclude={"element_tag"})
        case "main-image-performance":
            query_id = models.TemplateMainImageData.QUERY_ID
            query_params = lib.query_builder.template.query_context_to_template_params(shop_domain, query_context).model_dump(exclude={"element_tag"})
        case "duration-range-analysis":
            query_id = models.TemplateDurationDistributeData.QUERY_ID
            query_params = lib.query_builder.template.query_context_to_template_params(shop_domain, query_context).model_dump()
        case _:
            query_id = 0
            query_params = {}

    base = f"https://usq-redash.infra.leyantech.com/queries/{query_id}?p_shop_domain={query_params['shop_domain']}"
    base += f"&p_timestamp={query_params['timestamp']['start']}--{query_params['timestamp']['end']}"
    if product_id := query_params.get("product_id"):
        base += f"&p_product_id={product_id}"
    if element_tag := query_params.get("element_tag"):
        base += f"&p_element_tag={element_tag}"

    results = {
        "type": "sql_parser",
        "template_name": template_name,
        "shop_domain": shop_domain,
        "path": base,
        "query_context": query_context.model_dump_json(indent=2),
    }

    return templates.TemplateResponse("debug.html", {"request": request, "results": results, "curl_command": curl_command})


@router.post("/debug/session-reveal", response_class=HTMLResponse)
async def debug_session_reveal(
        request: Request,
        curl_command: str = Form(...),
        ch_pool: ChPool = Depends(deps.get_ch_pool),
):
    try:
        parsed = parse_curl_info(curl_command)
    except Exception as e:
        return templates.TemplateResponse(
            "debug.html",
            {"request": request, "error": str(e), "curl_command": curl_command}
        )
    shop_domain = parsed["shop_domain"]
    query_context = parsed["query_context"]

    try:
        from jinja2 import Template
        SQL = Template("""\
        SELECT DISTINCT sg_session_id FROM pixel_intersection_events
        WHERE shop_domain='{{ shop_domain }}' AND {{ time_range }}
            {% if product_id %} AND product_id = '{{ product_id }}'{% endif %}
            {% if element_tag %} AND element_tag = '{{ element_tag }}'{% endif %}
        LIMIT 1000
        """).render(
            shop_domain=shop_domain,
            time_range=lib.query_builder.template_query_builder.PixelEvents.time_range_to_sql(query_context.global_filters.time_range),
            product_id=query_context.product_id,
            element_tag=query_context.element_tag
        )
        sessions = await lib.ch.query_all_scalar(query=SQL, conn_pool=ch_pool)
        results = {
            "type": "session_reveal",
            "sql": SQL,
            "session_data": json.dumps(sessions, ensure_ascii=False, indent=2)
        }
        return templates.TemplateResponse("debug.html", {"request": request, "results": results, "curl_command": curl_command})
    except Exception as e:
        return templates.TemplateResponse("debug.html", {"request": request, "error": f"Failed to decode token: {e}", "curl_command": curl_command})

