from typing import Annotated

from asynch import Connection as ChConnection
from fastapi import APIRouter, Depends

from src.shop_bi import deps

router = APIRouter(prefix="/health", tags=["健康检查"])


@router.get("")
@router.get("/")
async def health_check(
    ch_conn: Annotated[ChConnection, Depends(deps.get_ch_conn)],
):
    """健康检查端点"""
    try:
        async with ch_conn.cursor() as cursor:
            await cursor.execute("SELECT 1")
            ret = await cursor.fetchone()
            assert ret == (1,)
        return {
            "status": "healthy",
            "database": "connected"
        }
    except:  # noqa
        return {
            "status": "unhealthy",
            "database": "disconnected"
        }
