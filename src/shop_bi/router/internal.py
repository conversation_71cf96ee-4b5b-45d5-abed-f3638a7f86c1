from typing import Annotated
from fastapi import APIRouter, Depends, Request, BackgroundTasks
from loguru import logger
from sqlmodel.ext.asyncio.session import AsyncSession

from src.shop_bi import deps
from src.lib.session_replay.session_replay import SessionReplay
from src.lib.session_replay.cos_client import cos_client


router = APIRouter(prefix="/internal", tags=["报表数据查询"])


@router.get("/session-replays/sessions/{session_id}")
async def get_session_replays(
    session_id: str,
    shop_bi_session: Annotated[AsyncSession, Depends(deps.get_shop_bi_db_session)]):
    logger.info(session_id)
    session_replays = await SessionReplay.get_session_replays(
        shop_bi_session, session_id
    )
    download_urls = await cos_client.batch_replay_urls(
        {
            replay.oss_prefix()
            for replay in session_replays
        }
    )
    return {
        "session_id": session_id,
        "replays": [
            {
                "replay_id": replay.replay_id,
                "download_urls": download_urls.get(replay.oss_prefix(), [])
            }
            for replay in session_replays
        ]
    }


@router.post("/session-replays")
async def create_session_replay(
    request: Request,
    session_replay: SessionReplay,
    shop_bi_session: Annotated[AsyncSession, Depends(deps.get_shop_bi_db_session)],
    background_tasks: BackgroundTasks
):
    with logger.contextualize(**session_replay.model_dump()):
        logger.info("创建replay")
    row_data = await request.body()
    if not session_replay.is_valid():
        logger.error("数据不完全")
        return {"success": False}
    await SessionReplay.batch_save_replays(
        shop_bi_session, {
            "sg_session_id": session_replay.sg_session_id,
            "replay_id": session_replay.replay_id,
            "shop_domain": session_replay.shop_domain,
            "sg_new_client_id": session_replay.sg_new_client_id
        }
    )
    background_tasks.add_task(cos_client.upload_data, session_replay.oss_key(), row_data)
    return {"success": True}
