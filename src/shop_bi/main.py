from contextlib import asynccontextmanager

import sentry_sdk
from fastapi import <PERSON><PERSON><PERSON>
from fastapi.routing import APIRoute
from fastapi.middleware.cors import CORSMiddleware

from leyan_logging import setup
from src.config import settings
from src.lib import ch
from src.shop_bi.router import bi, health, debug, internal


def custom_generate_unique_id(route: APIRoute) -> str:
    return f"{route.tags[0]}-{route.name}"


if settings.SENTRY_DSN and settings.ENVIRONMENT != "local":
    sentry_sdk.init(dsn=str(settings.SENTRY_DSN), enable_tracing=True)


@asynccontextmanager
async def lifespan(app: FastAPI):
    setup()
    if settings.CLICKHOUSE_URL and settings.ENVIRONMENT != "local":
        await ch.pool.startup()
        app.state.ch_pool = ch.pool
        yield
        await ch.pool.shutdown()
    else:
        yield


app = FastAPI(
    title="Shop Insight Web API",
    openapi_url=f"{settings.API_V1_STR}/openapi.json",
    generate_unique_id_function=custom_generate_unique_id,
    lifespan=lifespan,
)

# Set all CORS enabled origins
app.add_middleware(
    CORSMiddleware,
    allow_origins="*",
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

app.include_router(bi.router, prefix=settings.API_V1_STR)
app.include_router(health.router, prefix=settings.API_V1_STR)
app.include_router(debug.router)
app.include_router(internal.router, prefix=settings.API_V1_STR)
