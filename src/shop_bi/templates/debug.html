<!DOCTYPE html>
<html>
<head>
    <title>Debug Tool</title>
    <style>
        body { font-family: sans-serif; margin: 2em; }
        h1, h2 { color: #333; }
        .tab { overflow: hidden; border: 1px solid #ccc; background-color: #f1f1f1; }
        .tab button { background-color: inherit; float: left; border: none; outline: none; cursor: pointer; padding: 14px 16px; transition: 0.3s; }
        .tab button:hover { background-color: #ddd; }
        .tab button.active { background-color: #ccc; }
        .tabcontent { display: none; padding: 6px 12px; border: 1px solid #ccc; border-top: none; }
        form { margin-bottom: 2em; }
        textarea { width: 100%; box-sizing: border-box; }
        .results { border: 1px solid #ccc; padding: 1em; border-radius: 5px; }
        .error { color: red; font-weight: bold; }
        pre { background-color: #f4f4f4; padding: 1em; border-radius: 3px; white-space: pre-wrap; word-wrap: break-word; }
        code { font-family: monospace; }
    </style>
</head>
<body>
    <h1>Debug Tool</h1>

    <div class="tab">
        <button class="tablinks" onclick="openTool(event, 'SQLParser')">SQL Parser</button>
        <button class="tablinks" onclick="openTool(event, 'SessionReveal')">Session Reveal</button>
    </div>

    <div id="SQLParser" class="tabcontent">
        <h2>SQL Parser</h2>
        <form action="/shop-bi/debug/sql-parser" method="post">
            <textarea name="curl_command" rows="10" placeholder="Paste curl command here...">{{ curl_command }}</textarea>
            <br>
            <input type="submit" value="Debug">
        </form>
    </div>

    <div id="SessionReveal" class="tabcontent">
        <h2>Session Reveal</h2>
        <form action="/shop-bi/debug/session-reveal" method="post">
            <textarea name="curl_command" rows="10" placeholder="Paste curl command here...">{{ curl_command }}</textarea>
            <br>
            <input type="submit" value="Reveal">
        </form>
    </div>

    {% if error %}
        <div class="results error">
            <h2>Error</h2>
            <p>{{ error }}</p>
        </div>
    {% endif %}

    {% if results %}
        <div class="results">
            <h2>Debug Results</h2>

            {% if results.type == 'sql_parser' %}
                <h3>Parsed Information</h3>
                <p><strong>Template Name:</strong> {{ results.template_name }}</p>
                <p><strong>Shop Domain:</strong> {{ results.shop_domain }}</p>

                <h3>Query Context</h3>
                <pre><code>{{ results.query_context }}</code></pre>
                <pre><a href="{{ results.path }}" target="_blank">前往查看</a></pre>

            {% elif results.type == 'session_reveal' %}
                <h3>Session Data</h3>
                <pre><code>{{ results.session_data }}</code></pre>
                <h3>Query SQL</h3>
                <pre><code>{{ results.sql }}</code></pre>
            {% endif %}
        </div>
    {% endif %}

    <script>
        function openTool(evt, toolName) {
            var i, tabcontent, tablinks;
            tabcontent = document.getElementsByClassName("tabcontent");
            for (i = 0; i < tabcontent.length; i++) {
                tabcontent[i].style.display = "none";
            }
            tablinks = document.getElementsByClassName("tablinks");
            for (i = 0; i < tablinks.length; i++) {
                tablinks[i].className = tablinks[i].className.replace(" active", "");
            }
            document.getElementById(toolName).style.display = "block";
            evt.currentTarget.className += " active";
        }
        // Get the element with id="defaultOpen" and click on it
        document.getElementsByClassName("tablinks")[0].click();
    </script>

</body>
</html>