from typing import cast, Annotated

import jwt
from asynch import Connection as ChConnection
from fastapi import Depends, Request
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials

from src import models, lib

security = HTTPBearer()


async def get_shop_pupil_db_session():
    async with lib.shop_pupil.get_session() as session:
        yield session


async def get_knowledge_db_session():
    async with lib.knowledge.get_session() as session:
        yield session


async def get_shop_bi_db_session():
    async with lib.session_replay.get_session() as session:
        yield session


async def get_store_center_db_session():
    async with lib.store_center.get_session() as session:
        yield session


async def get_ch_pool(request: Request):
    yield request.app.state.ch_pool


async def get_ch_conn(request: Request):
    """从 clickhouse connection pool 中获取一个 connection"""
    pool = request.app.state.ch_pool
    async with pool.connection() as conn:
        yield cast(ChConnection, conn)


async def get_current_user(credentials: Annotated[HTTPAuthorizationCredentials, Depends(security)]):
    payload_obj = jwt.decode(credentials.credentials, algorithms=["HS256"], options={"verify_signature": False})
    payload = models.JWTPayload.model_validate(payload_obj)
    return payload


async def shop_domain(jwt_payload: Annotated[models.JWTPayload, Depends(get_current_user)]):
    """从用户授权信息中获取站点信息"""
    return jwt_payload.storeInfo.domain
