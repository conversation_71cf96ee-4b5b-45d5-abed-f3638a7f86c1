import asyncio
import logging
import time
import socketserver
from loguru import logger


class HealthCheckHandler(socketserver.BaseRequestHandler):
    def handle(self) -> None:
        _ = self.request.recv(1024)
        logger.debug("helth check")
        self.request.sendall(b"OK")


async def healthcheck(port=9021):
    service = socketserver.TCPServer(
        ("0.0.0.0", port), HealthCheckHandler)
    loop = asyncio.get_event_loop()
    loop.run_in_executor(None, service.serve_forever)
