import asyncio
import json
from json.decoder import JSONDecodeError

from asyncio import Event

from confluent_kafka import Consumer, KafkaException, KafkaError
from leyan_logging import setup
from loguru import logger
from src.health_check import healthcheck
from src.config import settings
from src.lib.session_replay.processor import ReplayProcessor


def error_cb(err):
    """卡夫卡异常函数"""
    logger.error(f"Client error: {err}")
    if err.code() == KafkaError._ALL_BROKERS_DOWN or err.code() == KafkaError._AUTHENTICATION:
        raise KafkaException(err)


class ReplayDataConsumer:
    """
    1. 暂时不考虑多个topic的问题
    2. 暂时不考虑内存问题
    """
    REPLAY_NAME = "sg:replay-events"

    def __init__(
        self,
        bootstrap_servers: str,
        processors: list,
        offset_reset="earliest",
    ):
        self.processors = {
            processor.topic: processor
            for processor in processors
        }
        self._consumer = Consumer(
            {
                # see: https://docs.confluent.io/platform/current/clients/confluent-kafka-python/html/index.html#pythonclient-configuration
                "bootstrap.servers": bootstrap_servers,
                "error_cb": error_cb,
                "auto.offset.reset": offset_reset,
                "enable.auto.commit": True,
                "group.id": "shop-bi",
            }
        )
        self._consumer.subscribe(list(self.processors.keys()))
        self.consume_event = Event()
        self.work_event = Event()
        self.records = {}

    def serialize(self, message):
        value = json.loads(message.value())
        message.set_value(value)

    def poll(self):
        """
        从kafka获取消息的间隔为1s, 1s后超时
        """
        message = self._consumer.poll(1)
        if message is None:
            logger.debug("message timeout")
            return None
        error = message.error()
        if error:
            if error.code() == KafkaError._PARTITION_EOF:
                logger.debug("partition eof")
                return None
            else:
                logger.critical("encounter unknown error: {error}")
                raise KafkaException(f"encounter unknown error: {error}")
        try:
            self.serialize(message)
        except JSONDecodeError as e:
            logger.error("kafka消息解析失败")
            raise e
        return message

    async def consume(self):
        logger.info("启动kafka消费者")
        loop = asyncio.get_event_loop()
        while await asyncio.wait_for(self.consume_event.wait(), timeout=10):
            self.work_event.set()
            message = await loop.run_in_executor(None, self.poll)
            if message:
                messages = self.records.setdefault(
                    message.topic(), []
                )
                messages.append(message.value())
                if len(messages) > settings.KAFKA_MESSAGE_BATCH:
                    self.consume_event.clear()

    async def work(self):
        while self.work_event.is_set():
            logger.info("开始执行消息解析/存储操作")
            if self.records:
                records, self.records = self.records, {}
                saved = await self.save_records(records)
                logger.info(f"write number {saved}")
            else:
                await asyncio.sleep(1)
            # 消费消息之后需要clear, 等待从kafka获取消息，避免后台任务中断
            self.consume_event.set()

    async def save_records(self, records):
        count = 0
        async with asyncio.TaskGroup() as tg:
            for topic, messages in records.items():
                processor = self.processors[topic]
                tg.create_task(processor.process(messages))
                count += len(messages)
        return count

    async def start(self):
        self.work_event.set()
        self.consume_event.set()
        await self.work()


async def main():
    consumer = ReplayDataConsumer(
        bootstrap_servers=",".join(settings.KAFKA_BOOTSTRAP_SERVERS),
        processors=[ReplayProcessor()])
    task1 = asyncio.create_task(consumer.consume())
    task2 = asyncio.create_task(healthcheck())
    await consumer.start()
    task1.cancel()
    task2.cancel()


if __name__ == "__main__":
    setup()
    asyncio.run(main())
