import os
from typing import List

from pydantic import Field
from pydantic_settings import BaseSettings
from loguru import logger

if os.environ.get('APOLLO_META'):
    from lepollo import get_config

    for key, value in get_config('application')._properties.items():
        os.environ[key] = value
    logger.info('loaded application config from Apollo')


class Settings(BaseSettings):
    ENVIRONMENT: str = Field(default="local", alias="env")
    API_V1_STR: str = "/api/v1"
    SENTRY_DSN: str = ""
    SHOP_PUPIL_DB_URL: str = "postgresql+asyncpg://postgres:postgres@192.168.1.180:15432/shopify_knowledge"
    SHOPIFY_KNOWLEDGE_DB_URL: str = "postgresql+asyncpg://postgres:postgres@192.168.1.180:15432/shopify_knowledge"
    SHOP_BI_DB_URL: str = "postgresql+asyncpg://postgres:postgres@127.0.0.1:5432/shop_bi"
    STORE_CENTER_DB_URL: str = "postgresql+asyncpg://postgres:postgres@192.168.1.180:15432/shopify_knowledge"
    CLICKHOUSE_URL: str = "clickhouse://ch_user:P@55w0rD:@127.0.0.1:9000/chdb"
    COS_SECRET_ID: str = "<SECRET ID>"
    COS_SECRET_KEY: str = "<SECRET KEY>"
    COS_REGION: str = "ap-nanjing"
    COS_BUCKET: str = "shop-pupil-1252160878"
    KAFKA_BOOTSTRAP_SERVERS: List[str] = Field(default_factory=lambda: ["10.13.32.22:9092"])
    KAFKA_MESSAGE_BATCH: int = 10
    REDASH_URL: str = "https://usq-redash.infra.leyantech.com/"
    REDASH_API_KEY: str = ""

    class Config:
        case_sensitive = True


settings = Settings()
