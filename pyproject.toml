[project]
name = "shop-bi"
version = "0.1.0"
description = "Add your description here"
readme = "README.md"
requires-python = ">=3.13"
dependencies = [
    "aiomysql>=0.2.0",
    "aiosqlite>=0.21.0",
    "asynch>=0.3.0",
    "asyncpg>=0.30.0",
    "common-libs",
    "fastapi[standard]>=0.116.1",
    "greenlet>=3.2.3",
    "jinja2>=3.1.6",
    "loguru>=0.7.3",
    "notebook>=7.4.4",
    "pydantic-settings>=2.10.1",
    "pyjwt>=2.10.1",
    "sentry-sdk>=2.34.0",
    "sqlglot[rs]>=27.6.0",
    "sqlmodel>=0.0.24",
    "uvicorn>=0.35.0",
    "ipython",
    "confluent-kafka>=2.11.0",
    "cos-python-sdk-v5>=1.9.38",
    "httpx>=0.27.0",
]

[project.scripts]
shell = "scripts.shell:run"


[[tool.uv.index]]
url = 'https://mirrors.tuna.tsinghua.edu.cn/pypi/web/simple'
default = true

[[tool.uv.index]]
url = 'https://readonlyuser:<EMAIL>/repository/pypi-all/simple'

[tool.ruff]
line-length = 120
indent-width = 4
target-version = "py313"

[dependency-groups]
dev = [
    "pytest>=8.4.1",
    "pytest-dotenv",
    "pytest-asyncio"
]
