appname: shop-bi

build:
  base: registry.leyantech.com/base-images/python:3.13
  prepare:
    version:
      files:
        - uv.lock
    script:
      - pip install uv
      - uv sync --no-dev

proc.web:
  type: web
  cmd: uv run uvicorn --host 0.0.0.0 --port 8000 src.shop_bi.main:app --forwarded-allow-ips '*'
  port: 8000
  memory: 512MB
  num_instances: 1
  healthcheck: 'http://localhost:8000/api/v1/health'

proc.kafka:
  cmd: uv run python -m src.kafka.consumer
  memory: 512MB
  num_instances: 1
  healthcheck:
    tcpSocket:
      port: '9021'
    initialDelaySeconds: 30
    periodSeconds: 5
    successThreshold: 1
    failureThreshold: 3
    timeoutSeconds: 5

