import pytest
import asyncio
from src.lib.session_replay.session_replay import SessionReplay
from src.lib.session_replay import get_session


@pytest.mark.asyncio
async def test_batch_insert_session_replay():
    datas = [
        {
            "sg_session_id": f"session-id-{i}",
            "replay_id": f"replay-id-{j}",
            "shop_domain": f"store-domain-{i}"
        }
        for i in range(10)
        for j in range(10)
    ]
    async with get_session() as session:
        await SessionReplay.batch_save_replays(session, datas)


@pytest.mark.asyncio
async def test_batch_get_session():
    session_id = "session-id-1"
    async with get_session() as session:
        session_replays = await SessionReplay.get_session_replays(session, session_id)
        assert len(session_replays) == 10
        for session_replay in session_replays:
            assert session_replay.shop_domain == "store-domain-1"
