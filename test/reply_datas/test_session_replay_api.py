import json
import uuid
from fastapi.testclient import TestClient

from src.shop_bi.main import app


async def test_create_replay(cos_client_mock):
    client = TestClient(app)
    data = {
        "sg_session_id": uuid.uuid4().hex,
        "replay_id": uuid.uuid4().hex,
        "shop_domain": "test-domain",
        "sg_new_client_id": uuid.uuid4().hex,
        "events": [{"rowdata": "a"}, {"rowdata": "b"}]
    }
    response = client.post(
        f"/api/v1/internal/session-replays",
        json=data
    )
    key_prefix = "{shop_domain}/{replay_id}/{sg_session_id}".format_map(data)

    assert response.status_code == 200
    assert response.json()["success"]
    for key, value in cos_client_mock.items():
        if key.startswith(key_prefix):
            assert json.loads(value) == data
            break
    else:
        raise RuntimeError("保存失败")


async def test_get_all_replays():  
    client = TestClient(app)
    response = client.get(
        f"/api/v1/internal/session-replays/sessions/session-id-1"
    )

    assert response.status_code == 200
    assert len(response.json()["replays"]) > 0
