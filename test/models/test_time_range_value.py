from datetime import timezone, timedelta
from src.constants import utc_tz
from src.models import TimeRangeValue


def test_simple_time_range_as_utc():
    time_range = TimeRangeValue(
        low="2025-08-01 00:00:00",
        high="2025-08-02 00:00:00"
    )
    assert time_range.low == "2025-08-01 00:00:00"
    assert time_range.high == "2025-08-02 00:00:00"
    assert time_range.tz == utc_tz


def test_iso_time_range():
    time_range = TimeRangeValue(
        low="2025-08-01T00:00:00+08:00",
        high="2025-08-02T00:00:00+08:00"
    )
    assert time_range.low == "2025-07-31 16:00:00"
    assert time_range.high == "2025-08-01 16:00:00"
    assert time_range.tz == timezone(timedelta(hours=8))
