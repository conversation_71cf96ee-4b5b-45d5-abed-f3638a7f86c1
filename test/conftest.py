import pytest
from src.lib.session_replay.cos_client import cos_client


@pytest.fixture(autouse=True)
def cos_client_mock(monkeypatch):
    """测试时不上传文件"""
    datas = {}
    def _replay_keys(prefix):
        return [f"{prefix}/{i}" for i in range(5)]

    def _get_presigned_url(key):
        return f"http://localhost/test/{key}"

    def _upload_small_data(key, value):
        datas[key] = value
        return "成功"

    def _upload_big_data(key, value):
        datas[key] = value
        return "成功"

    with monkeypatch.context() as m:
        m.setattr(cos_client, "_replay_keys", _replay_keys)
        m.setattr(cos_client, "_get_presigned_url", _get_presigned_url)
        m.setattr(cos_client, "_upload_big_data", _upload_big_data)
        m.setattr(cos_client, "_upload_small_data", _upload_small_data)
        yield datas
