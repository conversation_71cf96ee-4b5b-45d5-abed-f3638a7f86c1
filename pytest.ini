[pytest]
testpaths = test
asyncio_mode = auto
asyncio_default_fixture_loop_scope = session
asyncio_default_test_loop_scope = session
env_files = .env
addopts = -v -s --log-cli-level=DEBUG
log_cli = true
log_cli_level = INFO
log_cli_format = %(asctime)s [%(levelname)8s] %(message)s (%(filename)s:%(lineno)s)
log_cli_date_format = %Y-%m-%d %H:%M:%S
filterwarnings =
    ignore::RuntimeWarning
    ignore::ResourceWarning
