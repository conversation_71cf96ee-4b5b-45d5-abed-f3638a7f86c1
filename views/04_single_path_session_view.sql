CREATE OR REPLACE VIEW shopify.single_path_session_view ON CLUSTER 'default_cluster'
AS
WITH
{arg_from_timestamp:DateTime64(3)} AS arg_from_timestamp,
{arg_to_timestamp:DateTime64(3)} AS arg_to_timestamp,
{arg_shop_domain:String} AS arg_shop_domain,
detail AS (
    SELECT
        sg_session_id,
        COUNT(DISTINCT JSONExtractString(context, 'document', 'location', 'pathname')) AS path_count
    FROM shopify.pixel_events
    WHERE
        shop_domain = arg_shop_domain
        AND date BETWEEN toDate(arg_from_timestamp) AND toDate(arg_to_timestamp)
        AND timestamp BETWEEN arg_from_timestamp AND arg_to_timestamp
        AND name = 'page_viewed'
        AND JSONExtractString(context, 'navigator', 'userAgent') NOT LIKE '%Windows NT%'
        AND JSONExtractString(context, 'navigator', 'userAgent') NOT LIKE '%Macintosh%'
        AND JSONExtractString(context, 'navigator', 'userAgent') NOT LIKE '%X11%'
        AND J<PERSON>NExtractString(context, 'navigator', 'userAgent') NOT LIKE '%Linux x86_64%'
        AND JSONExtractString(context, 'navigator', 'userAgent') NOT LIKE '%Electron%'
    GROUP BY sg_session_id
    HAVING path_count = 1
)
SELECT DISTINCT sg_session_id FROM detail;