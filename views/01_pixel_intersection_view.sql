CREATE OR REPLACE VIEW shopify.pixel_intersection_view ON CLUSTER 'default_cluster'
AS
WITH
{arg_from_timestamp:DateTime64(3)} AS arg_from_timestamp,
{arg_to_timestamp:DateTime64(3)} AS arg_to_timestamp,
{arg_shop_domain:String} AS arg_shop_domain,
intersection_events AS (
    SELECT
        sg_event_id,
        sg_session_id,
        shop_domain,
        type,
        name,
        timestamp,
        sg_new_client_id,
        sg_element_id,
        element_tag,
        product_id,
        intersection_ratio,
        JSONExtractString(data, 'sgExtraData', 'main_image_index') AS main_image_index,
        lagInFrame(timestamp, 1) OVER w AS pre_timestamp,
        lagInFrame(coalesce(intersection_ratio, 1)) OVER w AS pre_intersection_ratio
    FROM shopify.pixel_intersection_events
    WHERE name = 'sg:dom-intersection'
        AND shop_domain = arg_shop_domain
        AND date BETWEEN toDate(arg_from_timestamp) AND toDate(arg_to_timestamp)
        AND timestamp BETWEEN arg_from_timestamp AND arg_to_timestamp
    WINDOW w AS (PARTITION BY shop_domain, sg_session_id, sg_element_id ORDER BY timestamp ASC)
),
filtered_events AS (
    SELECT *,
        CASE WHEN (pre_intersection_ratio < 0.5 OR pre_intersection_ratio IS NULL) AND intersection_ratio > 0.5 THEN 1 ELSE 0 END AS intersection_start,
        CASE WHEN pre_intersection_ratio > 0.5 AND intersection_ratio < 0.5 THEN 1 ELSE 0 END AS intersection_end
    FROM intersection_events
    WHERE (pre_intersection_ratio < 0.5 OR pre_intersection_ratio IS NULL) AND intersection_ratio > 0.5
        OR pre_intersection_ratio > 0.5 AND intersection_ratio < 0.5
)
SELECT 
    s.sg_event_id AS sg_event_id,
    s.sg_session_id AS sg_session_id,
    s.shop_domain AS shop_domain,
    s.type AS type,
    s.name AS name,
    s.sg_new_client_id AS sg_new_client_id,
    sg_element_id AS sg_element_id,
    s.element_tag AS element_tag,
    s.product_id AS product_id,
    s.main_image_index AS main_image_index,
    s.start_timestamp AS start_timestamp,
    e.end_timestamp AS end_timestamp,
    dateDiff('millisecond', s.start_timestamp, e.end_timestamp) AS intersection_duration
FROM 
    (SELECT sg_event_id, sg_session_id, shop_domain, type, name, sg_new_client_id, sg_element_id, element_tag, product_id, main_image_index, timestamp as start_timestamp,
        ROW_NUMBER() OVER (PARTITION BY shop_domain, sg_session_id, sg_element_id ORDER BY timestamp) AS rn
        FROM filtered_events WHERE intersection_start = 1) s
INNER JOIN 
    (SELECT sg_event_id, sg_session_id, shop_domain, type, name, sg_new_client_id, sg_element_id, element_tag, product_id, main_image_index, timestamp as end_timestamp,
        ROW_NUMBER() OVER (PARTITION BY shop_domain, sg_session_id, sg_element_id ORDER BY timestamp) AS rn
        FROM filtered_events WHERE intersection_end = 1) e
    ON s.shop_domain = e.shop_domain 
    AND s.sg_session_id = e.sg_session_id 
    AND s.sg_element_id = e.sg_element_id
    AND s.rn = e.rn
