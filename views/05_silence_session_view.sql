CREATE OR REPLACE VIEW shopify.silence_session_view ON CLUSTER 'default_cluster'
AS
WITH
{arg_from_timestamp:DateTime64(3)} AS arg_from_timestamp,
{arg_to_timestamp:DateTime64(3)} AS arg_to_timestamp,
{arg_shop_domain:String} AS arg_shop_domain,
detail AS (
    SELECT
        sg_session_id,
        SUM(
        CASE
            WHEN name IN ('sg:dom-intersection', 'sg:dom-touchstart', 'sg:dom-touchmove', 'sg:dom-touchend', 'sg:dom-touchcancel', 'product_viewed', 'collection_viewed', 'cart_viewed', 'sg:heartbeat', 'sg:replay-events') THEN 0
            ELSE 1
        END
        ) AS has_sg_dom
    FROM shopify.pixel_events
    WHERE
        shop_domain = arg_shop_domain
        AND date BETWEEN toDate(arg_from_timestamp) AND toDate(arg_to_timestamp)
        AND timestamp BETWEEN arg_from_timestamp AND arg_to_timestamp
        AND JSONExtractString(context, 'navigator', 'userAgent') NOT LIKE '%Windows NT%'
        AND JSONExtractString(context, 'navigator', 'userAgent') NOT LIKE '%Macintosh%'
        AND JSONExtractString(context, 'navigator', 'userAgent') NOT LIKE '%X11%'
        AND JSONExtractString(context, 'navigator', 'userAgent') NOT LIKE '%Linux x86_64%'
        AND JSONExtractString(context, 'navigator', 'userAgent') NOT LIKE '%Electron%'
    GROUP BY sg_session_id
    HAVING (has_sg_dom - 1) <= 0
)
SELECT DISTINCT sg_session_id FROM detail;